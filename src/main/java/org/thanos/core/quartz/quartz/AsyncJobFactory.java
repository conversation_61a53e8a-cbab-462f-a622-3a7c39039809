package org.thanos.core.quartz.quartz;

import java.io.IOException;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.thanos.core.quartz.model.ScheduleJob;
import org.thanos.core.quartz.vo.ScheduleJobVo;



/**
 * <AUTHOR>
 * @version  1.0
 * @since 2020-03-31
 * 
 */
public class AsyncJobFactory extends QuartzJobBean {

    /* 日志对象 */
    private static final Logger LOG = LoggerFactory.getLogger(AsyncJobFactory.class);
    @Override
	protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
		LOG.info("AsyncJobFactory execute start");
		ScheduleJob scheduleJob = (ScheduleJob) context.getMergedJobDataMap().get(ScheduleJobVo.JOB_PARAM_KEY);
		LOG.info("jobName:" + scheduleJob.getJobName() + "  " + scheduleJob);
		String url = scheduleJob.getUrl();
		CloseableHttpClient httpclient = HttpClients.createDefault();
		HttpGet httpGet = new HttpGet(url);
		CloseableHttpResponse response;
		try {
			response = httpclient.execute(httpGet);
			LOG.info(response.getStatusLine()+"");
			HttpEntity entity = response.getEntity();
			LOG.info("jobId:" + scheduleJob.getScheduleJobId() + "\r\n responseData:"
					+ EntityUtils.toString(entity, "utf-8"));
			EntityUtils.consume(entity);
		} catch (IOException e) {
			LOG.error("AsyncJobFactory execute failed:"+e.getMessage());
		}
		LOG.info("AsyncJobFactory execute end");
	}
}
