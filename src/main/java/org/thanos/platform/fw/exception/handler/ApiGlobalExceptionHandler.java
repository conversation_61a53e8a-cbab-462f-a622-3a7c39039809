/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：ApiGlobalExceptionHandler.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.exception.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.constants.AppConstant;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.constants.HttpStatusConstant;
import org.thanos.platform.fw.exception.AppBusinessException;
import org.thanos.platform.fw.exception.AppException;
import org.thanos.platform.fw.exception.AppSystemException;
import org.thanos.platform.fw.model.ClientResponseJsonBean;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.SQLDataException;
import java.util.List;

/**
 * 異常処理
 *
 * <AUTHOR>
 */
@ControllerAdvice
public class ApiGlobalExceptionHandler extends ResponseEntityExceptionHandler {

	@Autowired
	private ObjectMapper objectMapper;

	@Resource
	private MessageSource messageSource;

	/**
	 * msg
	 */
	@Autowired
	private LocaleMessageSourceService msgService;

	/** logger */
	private static final Logger logger = LoggerFactory.getLogger(ApiGlobalExceptionHandler.class);

	/**
	 * @param ex      MethodArgumentNotValidException
	 * @param headers HttpHeaders
	 * @param status  HttpStatus
	 * @param request WebRequest
	 * @return ResponseEntity<Object> 応答体
	 */
	@Override
	protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
			HttpHeaders headers, HttpStatus status, WebRequest request) {
		logger.debug(ex.getMessage());
		logMonitor(ex);
		List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
		ClientResponseJsonBean errBean = new ClientResponseJsonBean();
		String code = "";
		String message = "";
		for (FieldError fieldError : fieldErrors) {
			String fieldErrorCode = fieldError.getDefaultMessage();
			if ("A010201".equals(fieldErrorCode)) {
				code += fieldErrorCode + "  ";
				message += String.format(msgService.getMessage(MessageCode.SCOM10201), fieldError.getField()) + "  ";
			}
			if ("A010202".equals(fieldErrorCode)) {
				code += fieldErrorCode + "  ";
				message += String.format(msgService.getMessage(MessageCode.SCOM10202), fieldError.getField()) + "  ";
			}
			if ("A010203".equals(fieldErrorCode)) {
				code += fieldErrorCode + "  ";
				message += String.format(msgService.getMessage(MessageCode.SCOM10203), fieldError.getField()) + "  ";
			}
		}
		errBean.setRt("1");
		errBean.setCode(code.substring(0, code.length() - 1));
		errBean.setMessage(message.substring(0, message.length() - 1));
		return ResponseEntity.status(HttpStatusConstant.HTTP_500.getStatusCode()).body((Object) errBean);
	}

	/**
	 * @param ex      NullPointerException
	 * @param request WebRequest
	 * @return ResponseEntity<Object> 応答体
	 */
	@ExceptionHandler(NullPointerException.class)
	public ResponseEntity<Object> handleIOException(NullPointerException ex, WebRequest request)
			throws JsonProcessingException {
		return getResponse(CodeConstant.SCOM30003,ex.getMessage(),HttpStatusConstant.HTTP_500,ex);
	}

	/**
	 * アプリBusiness Exceptionの処理方法
	 *
	 * @param ex      AppBusinessException。
	 * @param request request
	 * @return ResponseEntity<Object> 応答体。
	 */
	@ExceptionHandler(AppBusinessException.class)
	public ResponseEntity<Object> handleBusinessException(AppBusinessException ex, WebRequest request)
			throws JsonProcessingException {
		return getResponse(CodeConstant.SCOM10102,ex.getMessage(),HttpStatusConstant.HTTP_500,ex);
	}

	/**
	 * system exception
	 *
	 * @param ex      exception handle
	 * @param request request
	 * @return error info
	 * @throws JsonProcessingException
	 */
	@ExceptionHandler(AppSystemException.class)
	@ResponseBody
	public ResponseEntity<Object> handleSystemException(AppSystemException ex, WebRequest request)
			throws JsonProcessingException {
		return getResponse(CodeConstant.SCOM10102,ex.getMessage(),HttpStatusConstant.HTTP_500,ex);
	}

	/**
	 * exception except business and system exception handle
	 *
	 * @param ex      exception
	 * @param request request
	 * @return error info
	 * @throws JsonProcessingException
	 */
	@ExceptionHandler(Exception.class)
	public ResponseEntity<Object> handleUncatchedError(Exception ex, WebRequest request)
			throws JsonProcessingException {
		return getResponse(CodeConstant.SCOM10102,ex.getMessage(),HttpStatusConstant.HTTP_500,ex);
	}

	@ExceptionHandler(SQLDataException.class)
	public ResponseEntity<Object> handleSQLDataException(SQLDataException ex, WebRequest request)
			throws JsonProcessingException {
		return getResponse(CodeConstant.SCOM10101,ex.getMessage(),HttpStatusConstant.HTTP_500,ex);
	}

	private ResponseEntity<Object> getResponse(CodeConstant codeConstant,String message,HttpStatusConstant status,Exception ex){
		logger.debug(message);
		ClientResponseJsonBean errBean = new ClientResponseJsonBean();
		errBean.setRt("1");
		errBean.setCode(codeConstant.getCode());
		errBean.setMessage(message);
		logMonitor(ex);
		return ResponseEntity.status(status.getStatusCode()).body((Object) errBean);
	}
	/**
	 * system exception handle
	 *
	 * @param ex      exception
	 * @param body    response body
	 * @param headers response header
	 * @param status  response status
	 * @param request request
	 */
	@Override
	protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers,
			HttpStatus status, WebRequest request) {
		logger.debug(ex.getMessage());
		if (ex instanceof BindException) {
			BindException exception = (BindException) ex;
			List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
			ClientResponseJsonBean errBean = new ClientResponseJsonBean();
			String code = "";
			String message = "";
			for (FieldError fieldError : fieldErrors) {
				String fieldErrorCode = fieldError.getDefaultMessage();
				if ("A010201".equals(fieldErrorCode)) {
					code += fieldErrorCode + ",";
					message += String.format(msgService.getMessage(MessageCode.SCOM10201), fieldError.getField()) + ",";
				}
				if ("A010202".equals(fieldErrorCode)) {
					code += fieldErrorCode + ",";
					message += String.format(msgService.getMessage(MessageCode.SCOM10202), fieldError.getField()) + ",";
				}
				if ("A010203".equals(fieldErrorCode)) {
					code += fieldErrorCode + ",";
					message += String.format(msgService.getMessage(MessageCode.SCOM10203), fieldError.getField()) + ",";
				}
			}
			errBean.setRt("1");
			errBean.setCode(code.substring(0, code.length() - 1));
			errBean.setMessage(message.substring(0, message.length() - 1));
			return ResponseEntity.status(HttpStatusConstant.HTTP_500.getStatusCode()).body((Object) errBean);
		}
		ClientResponseJsonBean errBean = new ClientResponseJsonBean();
		errBean.setRt("1");
		errBean.setCode(CodeConstant.SCOM30004.getCode());
		errBean.setMessage(ex.getMessage());
		logMonitor(ex);
		try {
			logger.error("response:" + objectMapper.writeValueAsString(errBean));
		} catch (JsonProcessingException e) {
			logger.error(e.getMessage());
		}
		return ResponseEntity.status(HttpStatusConstant.HTTP_500.getStatusCode()).body((Object) errBean);
	}

	/**
	 * @param ex      MissingPathVariableException
	 * @param headers HttpHeaders
	 * @param status  HttpStatus
	 * @param request WebRequest
	 * @return
	 */
	@Override
	protected ResponseEntity<Object> handleMissingPathVariable(MissingPathVariableException ex, HttpHeaders headers,
			HttpStatus status, WebRequest request) {
		logger.debug(ex.getMessage());
		ClientResponseJsonBean errBean = new ClientResponseJsonBean();
		errBean.setCode(CodeConstant.SCOM30004.getCode());
		errBean.setMessage(ex.getMessage());
		logMonitor(ex);
		try {
			logger.error("response:" + objectMapper.writeValueAsString(errBean));
		} catch (JsonProcessingException e) {
            logger.error(e.getMessage());
		}
		return ResponseEntity.status(HttpStatusConstant.HTTP_500.getStatusCode()).body((Object) errBean);
	}

	/**
	 * log output
	 *
	 * @param e exception
	 */
	private void logMonitor(Exception e) {
		if (e instanceof AppException) {
			String message = ((AppException) e).getErrMessage();
			String serviceEffect = AppConstant.MONITOR_LEVEL_1;

			if (AppException.class.isAssignableFrom(e.getClass())) {
				serviceEffect = ((AppException) e).getServiceEffect();
			}
			logger.error(serviceEffect + "," + getExceptionMessage(e));
		} else {
			String serviceEffect = AppConstant.MONITOR_LEVEL_3;
			logger.error(serviceEffect + "," + getExceptionMessage(e));
		}

	}
	/**
	 * 获取exception的详细错误信息。
	 */
	public static String getExceptionMessage(Throwable e)
	{
		StringWriter sw = new StringWriter();
		e.printStackTrace(new PrintWriter(sw, true));
		String str = sw.toString();
		return str;
	}

	public static String getRootErrorMessage(Exception e)
	{
		Throwable root = ExceptionUtils.getRootCause(e);
		root = (root == null ? e : root);
		if (root == null)
		{
			return "";
		}
		String msg = root.getMessage();
		if (msg == null)
		{
			return "null";
		}
		return StringUtils.defaultString(msg);
	}
}
