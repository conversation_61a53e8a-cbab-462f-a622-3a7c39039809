/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：SecurityFilter.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.auth.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.SecurityMetadataSource;
import org.springframework.security.access.intercept.AbstractSecurityInterceptor;
import org.springframework.security.access.intercept.InterceptorStatusToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.FilterInvocation;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.auth.damain.*;
import org.thanos.platform.fw.auth.service.SysTokenService;
import org.thanos.platform.fw.auth.service.SysUserService;
import org.thanos.platform.fw.auth.util.RedisKeyConst;
import org.thanos.platform.fw.auth.util.RedisUtil;
import org.thanos.platform.fw.auth.util.TokenGenerateUtil;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.constants.EnvironmentConstant;
import org.thanos.platform.fw.constants.HeaderContent;
import org.thanos.platform.fw.filter.MDCFilter;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.util.JsonUtil;

import javax.annotation.PostConstruct;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.UUID;

/**
 * 对受保护对象的访问进行拦截类
 *
 * <AUTHOR>
 */
public class SecurityFilter extends AbstractSecurityInterceptor implements Filter {

    private Logger logger = LoggerFactory.getLogger(SecurityFilter.class);

    private static final String FILTER_APPLIED = "__spring_security_filterSecurityInterceptor_filterApplied";

    @Autowired
    ResourceFilterInvocation resourceFilterInvocation;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    SecurityAccessDecisionManager securityAccessDecisionManager;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    EnvironmentConstant environmentConstant;

    @Autowired
    TokenGenerateUtil tokenGenerateUtil;

    @Autowired
    private LocaleMessageSourceService msgService;

    @Autowired
    SysTokenService sysTokenService;

    @Autowired
    SysUserService sysUserService;

    @Autowired
    WhiteTableService whiteTableService;

    @PostConstruct
    public void init() {
        super.setAuthenticationManager(authenticationManager);
        super.setAccessDecisionManager(securityAccessDecisionManager);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("init in Security ");
    }

    /**
     * 对url进行过滤
     *
     * @param request  ServletRequest。
     * @param response ServletResponse。
     * @param chain    FilterChain。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (request.getAttribute(FILTER_APPLIED) != null) {
            chain.doFilter(request, response);
            return;
        }

        request.setAttribute(FILTER_APPLIED, true);

        if (!environmentConstant.isEnable()) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest req = (HttpServletRequest) request;
        String requestUri = req.getRequestURI();

        String[] releaseResource = environmentConstant.getReleaseResource();
        for (String url : releaseResource) {
            if (requestUri.contains(url)) {
                chain.doFilter(request, response);
                return;
            }
        }

        logger.info(requestUri);
        String[] releaseUrl = environmentConstant.getReleaseUri();
        for (String url : releaseUrl) {
            if (requestUri.endsWith(url)) {
                chain.doFilter(request, response);
                return;
            }
        }

        if (whiteTableService.checkWhiteTable(req.getServletPath())) {
            logger.info("security filter white table access.");
            chain.doFilter(request, response);
            return;
        }

        // d判断是否微信请求
        if (isFormWeixin(req) && req.getHeader(ACCESS_TOKEN) != null
                && redisUtil.hasKey(req.getHeader(ACCESS_TOKEN))) {
            String accessToken = req.getHeader(ACCESS_TOKEN);
            logger.info("weixin accessToken:" + accessToken);
            if (checkWeixinToken(accessToken)) {
                chain.doFilter(request, response);
                return;
            }
        }

        if (!tokenCheck(req, (HttpServletResponse) response)) {
            logger.info("token check failed.");
            ClientResponseJsonBean ClientResponseJsonBean = new ClientResponseJsonBean();
            ClientResponseJsonBean.setRt("0");
            ClientResponseJsonBean.setCode(String.valueOf(CodeConstant.BAUT20102));
            ClientResponseJsonBean.setMessage(msgService.getMessage(MessageCode.BAUT20102));
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(JsonUtil.toJson(ClientResponseJsonBean));
            return;
        }
        logger.info("doFilter  in   security");
        FilterInvocation fi = new FilterInvocation(request, response, chain);
        InterceptorStatusToken token = null;
        try {
            token = super.beforeInvocation(fi);
            fi.getChain().doFilter(fi.getRequest(), fi.getResponse());
        } catch (AccessDeniedException accessDeniedException) {
            logger.info("Access is denied");
            throw accessDeniedException;
        } finally {
            super.afterInvocation(token, null);
        }
    }

    @Override
    public void destroy() {

    }

    @Override
    public Class<?> getSecureObjectClass() {
        return FilterInvocation.class;
    }

    private boolean tokenCheck(HttpServletRequest request, HttpServletResponse response) {
        String clientId = request.getHeader(HeaderContent.HEAD_CLIENT_ID);
        String accessToken = request.getHeader(HeaderContent.HEAD_ACCESS_TOKEN);

        // 添加调试日志
        logger.info("=== Token验证调试信息 ===");
        logger.info("请求路径: {}", request.getServletPath());
        logger.info("clientId: {}", clientId);
        logger.info("accessToken: {}", accessToken != null ? "存在" : "不存在");

        if (clientId == null || accessToken == null) {
            logger.info("缺少必要的请求头 clientId 或 accessToken");
            return false;
        }

        String redisKey = RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId));
        logger.info("Redis Key: {}", redisKey);
        logger.info("Redis中是否存在该Key: {}", redisUtil.hasKey(redisKey));

        // 如果Redis中不存在key，记录详细信息
        if (!redisUtil.hasKey(redisKey)) {
            logger.error("Redis中不存在token记录，Key: {}", redisKey);
            logger.error("请检查：1.用户是否已登录 2.Redis连接是否正常 3.token是否已过期");
            return false;
        }

        // 签名比较
        if (redisUtil.hasKey(redisKey)) {

            if (redisUtil.getExpire(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId))) <= 200) {
                redisUtil.expire(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId)));
            }

            try {
                SysRefreshToken sysRefreshToken = tokenGenerateUtil.decode(clientId, accessToken, false);

                logger.info(JsonUtil.toJson(sysRefreshToken));
                // decode failed //ip changed
                // !ConnectionUtil.getRemoteIp(request).equals(sysRefreshToken.getHost())
                if (sysRefreshToken == null) {
                    SysToken deleteData = new SysToken();
                    deleteData.setClientId(clientId);
                    deleteData.setEnable(0);
                    deleteData.setUpdateTime(Calendar.getInstance().getTime());
                    redisUtil.clearById(clientId);
                    sysTokenService.updateStatus(deleteData);
                    return false;
                }

                logger.info(" token 》" + redisUtil.get(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId)))
                        .equals(sysRefreshToken.getToken()));
                // token check
                if (redisUtil.get(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId)))
                        .equals(sysRefreshToken.getToken())) {
                    logger.info(" token 1");
                    if (environmentConstant.isTokenRefresh()) {
                        String tokenSign = UUID.randomUUID().toString();
                        String newToken = tokenGenerateUtil.getToken(tokenSign, clientId, sysRefreshToken.getUserName(),
                                sysRefreshToken.getUserId(), sysRefreshToken.getHost(), sysRefreshToken.getStoreId());
                        response.addHeader(HeaderContent.HEAD_ACCESS_TOKEN, newToken);
                        SysToken record = new SysToken();
                        record.setHost(sysRefreshToken.getHost());
                        record.setUserId(sysRefreshToken.getUserId());
                        record.setClientId(clientId);
                        record.setToken(tokenSign);
                        record.setEnable(1);
                        record.setUpdateTime(Calendar.getInstance().getTime());
                        sysTokenService.update(record);
                    }
                    // 如果是IOS请求url 缓存用户ID数据
                    if (request.getServletPath().startsWith("/ios")) {
                        MDC.put(MDCFilter.USER_ID, sysRefreshToken.getUserId());
                    }
                    return true;
                } else {
                    logger.info(" token 2");

                    SysToken deleteData = new SysToken();
                    deleteData.setClientId(clientId);
                    deleteData.setEnable(0);
                    deleteData.setUpdateTime(Calendar.getInstance().getTime());
                    deleteData.setUserId(sysRefreshToken.getUserId());
                    redisUtil.clearById(clientId);
                    sysTokenService.updateStatus(deleteData);
                }
            } catch (Exception e) {
                redisUtil.clearById(clientId);
                e.printStackTrace();
                logger.info("accessToken decode fail:" + accessToken + "=>tokenCheck :" + e.getMessage());
                return false;
            }
        }
        return false;
    }

    @Override
    public SecurityMetadataSource obtainSecurityMetadataSource() {
        return resourceFilterInvocation;
    }

    private static final String ACCESS_TOKEN = "accessToken";

    public boolean isFormUni(HttpServletRequest request) {
        String url = request.getRequestURI();
        if (url.contains("uni/")) {
            return true;
        }
        return false;
    }

    public boolean isFormWeixin(HttpServletRequest request) {
        String url = request.getRequestURI();
        if (url.contains("wx/")) {
            return true;
        }
        return false;
    }

    public boolean checkWeixinToken(String accessToken) {
        String userId = redisUtil.get(accessToken);
        SysUser userInfo = sysUserService.getById(userId);
        if (userInfo != null && userInfo.getEnabled() == 1) {
            return true;
        }
        return false;
    }

}
