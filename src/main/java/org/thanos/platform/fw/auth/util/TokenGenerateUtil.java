package org.thanos.platform.fw.auth.util;

import jp.co.nttdata.sz.web.entity.SysGlobalConfigEntity;
import jp.co.nttdata.sz.web.service.sysGlobalConfig.SysGlobalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.thanos.core.otpwc.OTPWCUtil;
import org.thanos.platform.fw.auth.damain.SysRefreshToken;
import org.thanos.platform.fw.constants.EnvironmentConstant;
import org.thanos.platform.fw.constants.EnvironmentMemoryConfigure;
import org.thanos.platform.fw.constants.HeaderContent;
import org.thanos.platform.util.DateFormatUtil;
import org.thanos.platform.util.JsonUtil;

import java.security.Key;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by cong.ding on 2020/04/08.
 */
@Slf4j
@Component
public class TokenGenerateUtil {


    @Autowired
    RedisUtil redisUtil;

    @Autowired
    private SysGlobalConfigService sysGlobalConfigService;

    @Autowired
    EnvironmentConstant environmentConstant;

    @Autowired
    EnvironmentMemoryConfigure environmentMemoryConfigure;
    private int keyCheckLength = 80;

    private int keyCheckStart = 40;

    public SysRefreshToken decode(String clientId, String token, boolean isQrCode) throws Exception {

        //entryResult(userName + "," + sign) + publicKey + size(splitBy + size1 +size2)
        SysRefreshToken refreshToken = new SysRefreshToken();
        byte[] decodeData = Base64Util.decodeByte(token);

        log.info(JsonUtil.toJson(decodeData));
        if (decodeData.length > 0) {
            if (StringUtils.isEmpty(decodeData)) {
                return null;
            }


            // String publicKey = decodeData.substring(decodeData.length() - keyCheckLength, decodeData.length());
//
//            if (StringUtils.isEmpty(publicKey)) {
//                return null;
//            }
            String checkRsaPublicKey = "";
            if (isQrCode) {
                checkRsaPublicKey = redisUtil.get(RedisKeyConst.getKey1(HeaderContent.getHeadQRCodeRsaKey(clientId)));
            } else {
                checkRsaPublicKey = redisUtil.get(RedisKeyConst.getKey1(HeaderContent.getHeadRsaKey(clientId)));
            }
            log.info("RedisKeyConst.getKey1 >" + RedisKeyConst.getKey1(HeaderContent.getHeadRsaKey(clientId)));
            log.info("checkRsaPublicKey >" + checkRsaPublicKey);
//            if (!getPublicKeyForCheck(checkRsaPublicKey).equals(publicKey)) {
//                return null;
//            }

            String userNameAndSign = new String(RSAUtil.decryptByPublicKey(decodeData, Base64Util.decodeByte(checkRsaPublicKey)));
            if (StringUtils.isEmpty(decodeData) || !userNameAndSign.contains(",")) {
                return null;
            }
            String[] authInfo = userNameAndSign.split(",");
            if (authInfo == null) {
                return null;
            }
            refreshToken.setUserName(authInfo[0]);
            refreshToken.setToken(authInfo[1]);
            refreshToken.setUserId(authInfo[2]);
            refreshToken.setHost(authInfo[3]);
            refreshToken.setClientId(clientId);
            refreshToken.setStoreId(authInfo[4]);
        }
        return refreshToken;
    }

    public String getQRCodeSecurityCode() {
        String securityCode = OTPWCUtil.SecurityCode_Create();
        return securityCode;
    }

    public boolean checkQRCodeOTPWC(String securityCode, String token) {

        String current = OTPWCUtil.OTPW_Create(Integer.valueOf(securityCode));

        if (current.equals(token)) {
            return true;
        }
        return false;
    }

    public String getQRCodeOTPWC(String securityCode, String storeId,String userId, Date expiryTime) {

        String date = DateFormatUtil.BETWEEN_FORMAT_HOUR_MIN.format(expiryTime);

        String token = OTPWCUtil.OTPW_Create(Integer.valueOf(securityCode));

        redisUtil.set(RedisKeyConst.getKey2("securityCode:"+storeId+":", token), userId, Integer.valueOf(environmentMemoryConfigure.getQrcodeExpiryMs()));

        return token + date;
    }

    @Deprecated
    public SysRefreshToken checkQRCodeToken(String token) {
        try {
            String decodeData = Base64Util.decode(token);
            if (!StringUtils.isEmpty(decodeData) && decodeData.contains(",")) {
                String[] tokenData = decodeData.split(",");
                return decode(tokenData[1], tokenData[0], true);
            }
        } catch (Exception e) {
            throw new RuntimeException("Base64 decode error.");
        }
        return null;
    }

    @Deprecated
    public String getQRCodeToken(String token, String clientId, String userName, String userId, String host) {
        String publicKey = "";
        String encodeTokenData = "";
        try {
            Map<String, Key> keyMap = RSAUtil.initKey();
            byte[] puk = RSAUtil.getPublicKeyByte(keyMap);
            byte[] pik = RSAUtil.getPrivateKeyByte(keyMap);
            publicKey = Base64Util.encode(puk);
            String entryData = userName + "," + token + "," + userId + "," + host;
            encodeTokenData = Base64Util.encode(generateToken(publicKey, pik, entryData) + "," + clientId);
            redisUtil.set(RedisKeyConst.getKey1(HeaderContent.getHeadQRCodeTokenKey(clientId)), token);
        } catch (Exception e) {
            throw new RuntimeException("RSAUtil initKey failed.");
        }
        redisUtil.set(RedisKeyConst.getKey1(HeaderContent.getHeadQRCodeRsaKey(clientId)), publicKey);
        return encodeTokenData;
    }

    public String getToken(String token, String clientId, String userName, String userId, String host, String storeId) {
        String publicKey = "";
        String tokenData = "";
        try {
            Map<String, Key> keyMap = RSAUtil.initKey();
            byte[] puk = RSAUtil.getPublicKeyByte(keyMap);
            byte[] pik = RSAUtil.getPrivateKeyByte(keyMap);
            publicKey = Base64Util.encode(puk);
            String entryData = userName + "," + token + "," + userId + "," + host+ "," + storeId;
            tokenData = generateToken(publicKey, pik, entryData);
            //获取登录token的有效时间（单位：秒）
            SysGlobalConfigEntity sysGlobalConfig = sysGlobalConfigService.getConfigByCode("login_token_validity_time",storeId);
            if (sysGlobalConfig == null) {
                redisUtil.set(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId)), token, TimeUnit.SECONDS);
            } else {
                try {
                    int loginTokenValidityTime = Integer.parseInt(sysGlobalConfig.getConfig());
                    redisUtil.set(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId)), token, loginTokenValidityTime, TimeUnit.SECONDS);
                } catch (Exception e) {
                    redisUtil.set(RedisKeyConst.getKey1(HeaderContent.getHeadTokenKey(clientId)), token, TimeUnit.SECONDS);
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("RSAUtil initKey failed.");
        }
        redisUtil.set(RedisKeyConst.getKey1(HeaderContent.getHeadRsaKey(clientId)), publicKey);
        return tokenData;
    }

    public String getToken(String clientId, String userName, String userId, String host, String storeId) {
        String sign = UUIDGenerateUtil.getUUID();
        return getToken(sign, clientId, userName, userId, host,storeId);
    }

    private String generateToken(String publicKey, byte[] privateKey, String entryData) throws Exception {

        //userName + "," + sign
        String entryResult = Base64Util.encode(RSAUtil.encryptByPrivateKey(entryData.getBytes(), privateKey));
//
//        return Base64Util.encode(entryResult + getPublicKeyForCheck(publicKey));
        return entryResult;
    }

    private String getPublicKeyForCheck(String publicKey) {
        if (StringUtils.isEmpty(publicKey) || publicKey.length() < 15) {
            throw new RuntimeException("publicKey generate failed.");
        }
        return publicKey.substring(keyCheckStart, keyCheckStart + keyCheckLength);
    }

}
