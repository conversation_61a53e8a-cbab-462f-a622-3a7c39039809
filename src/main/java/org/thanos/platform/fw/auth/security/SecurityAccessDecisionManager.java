/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：SecurityAccessDecisionManager.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.auth.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;

/**
 * 判断用户是否具有访问权限。
 *
 * <AUTHOR>
 */
@Component
public class SecurityAccessDecisionManager implements AccessDecisionManager {

    private Logger logger = LoggerFactory.getLogger(SecurityAccessDecisionManager.class);

    @Autowired
    WhiteTableService whiteTableService;
    /**
     * 要求アドレスを返すために必要な権限
     *
     * @param authentication   Authentication。
     * @param object           Object。
     * @param configAttributes Collection<ConfigAttribute>。
     */
    @Override
    public void decide(Authentication authentication, Object object, Collection<ConfigAttribute> configAttributes)
            throws AccessDeniedException, InsufficientAuthenticationException {

        if (configAttributes == null) {
            return;
        }

        if(object instanceof FilterInvocation){
            String path = ((FilterInvocation)object).getRequest().getServletPath();
            logger.info("检查特殊路径处理 - ServletPath: {}", path);
            //logger.info("用户个人信息",authentication);
            if(path.startsWith("/ios") || path.contains("/ios/")){
                logger.info("iOS路径直接放行: {}", path);
                return;
            }
//            todo 权限问题先全部放开

            // 添加临时解决方案：如果是order相关接口且用户是ROLE_ANONYMOUS，提供更详细的错误信息
            if(path.contains("/order/") && "anonymousUser".equals(authentication.getName())) {
                logger.error("=== 订单接口权限问题分析 ===");
                logger.error("接口路径: {}", path);
                logger.error("用户身份: {} (匿名用户)", authentication.getName());
                logger.error("用户权限: {}", authentication.getAuthorities());
                logger.error("问题原因: 用户token验证失败，被识别为匿名用户");
                logger.error("解决建议: 1.检查客户端是否发送了正确的clientId和accessToken请求头");
                logger.error("解决建议: 2.检查token是否过期或无效");
                logger.error("解决建议: 3.检查用户是否已正确登录");
            }
        }
        if(true){
            return;
        }
        Iterator<ConfigAttribute> ite = configAttributes.iterator();
        // 判断用户持有的权限是否符合对应的Url权限，如果实现UserDetailsService，则用户权限为loadUserByUsername返回对应用户的权限。


        // 添加调试日志
        logger.info("=== 权限验证调试信息 ===");
        logger.info("用户名: {}", authentication.getName());
        logger.info("用户拥有的权限:");




        for (GrantedAuthority ga : authentication.getAuthorities()) {
            logger.info("  - {}", ga.getAuthority());
        }
        logger.info("URL需要的权限:");

        while (ite.hasNext()) {
            ConfigAttribute ca = ite.next();
            String needRole = ca.getAttribute();
            logger.info("  - 需要权限: {}", needRole);
            for (GrantedAuthority ga : authentication.getAuthorities()) {
                logger.info("    检查用户权限: {} vs 需要权限: {}", ga.getAuthority(), needRole);
                if (needRole.equals(ga.getAuthority())) {
                    logger.info("权限匹配成功！");
                    return;
                }
            }
        }
        logger.info("AccessDecisionManager:権限が足りません");
        throw new AccessDeniedException("AccessDecisionManager:権限が足りません");
    }

    @Override
    public boolean supports(ConfigAttribute attribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return true;
    }

}
