/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：DefaultAuthenticationFailureHandler.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.auth.security.authentication;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.util.JsonUtil;
import org.thanos.platform.util.ResultDataUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * ユーザ登録認証失敗の処理
 *
 * <AUTHOR>
 */
@Component
public class DefaultAuthenticationFailureHandler implements AuthenticationFailureHandler {

	/**
	 * msg
	 */
	@Autowired
	private LocaleMessageSourceService msgService;
	

    /**
     * ユーザ登録認証失敗の処理
     *
     * @param request   HttpServletRequest。
     * @param response  HttpServletResponse。
     * @param exception 認証エラー。
     */
    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException, ServletException {
        ClientResponseJsonBean ClientResponseJsonBean = new ClientResponseJsonBean();
        ClientResponseJsonBean.setRt(ResultDataUtil.RT.NG.code);
        if (exception instanceof UsernameNotFoundException||exception instanceof BadCredentialsException) {
            ClientResponseJsonBean.setCode(String.valueOf(CodeConstant.BAUT20101));
            ClientResponseJsonBean.setMessage(msgService.getMessage(MessageCode.BAUT20101));
        }else if(exception instanceof DisabledException){
            ClientResponseJsonBean.setCode(String.valueOf(CodeConstant.BAUT20105));
            ClientResponseJsonBean.setMessage(msgService.getMessage(MessageCode.BAUT20105));
        }else{
            ClientResponseJsonBean.setCode(String.valueOf(CodeConstant.BAUT20102));
            ClientResponseJsonBean.setMessage(msgService.getMessage(MessageCode.BAUT20102));
        }
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(JsonUtil.toJson(ClientResponseJsonBean));
    }
}
