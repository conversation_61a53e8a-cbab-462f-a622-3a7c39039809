/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：SysUserRoleService.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.auth.service;

import org.thanos.platform.fw.auth.damain.SysUserRole;

import java.util.List;
import java.util.Map;

/**
 * ユーザーキャラクターサービス
 *
 * <AUTHOR>
 */

public interface SysUserRoleService {

    /**
     * ユーザIDでユーザキャラクターを取得する。
     *
     * @param userId ユーザID
     * @return List<SysUserRole> ユーザキャラクター。
     */
    List<SysUserRole> listByUserId(String userId);

    /**
     * 查询角色所有信息 (可用only).
     *
     * @param userId
     * @return
     */
    List<Map<String, Object>> getSysUserRoleByUserId(String userId);

    /**
     * 更新用户角色信息.
     *
     * @param userId
     * @param roles
     * @return
     */
    int updUserRoleByUserId(String userId, String roles);
}
