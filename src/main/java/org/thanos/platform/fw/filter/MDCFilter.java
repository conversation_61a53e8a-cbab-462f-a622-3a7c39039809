/*
 * システム名：Aquarea
 * 業務名：メッセージ情報の操作用のユーティリティ
 * ファイル名：MDCFilter.java
 * 著作権表記：
 * Copyright (C) 2019 NCIT CORPORATION
 * 作成履歴
 * 20190902/NCIT/新規作成
 */
package org.thanos.platform.fw.filter;

import io.micrometer.core.instrument.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import org.thanos.platform.fw.auth.damain.SysClientType;
import org.thanos.platform.fw.constants.EnvironmentConstant;
import org.thanos.platform.fw.constants.HeaderContent;
import org.thanos.platform.util.ConnectionUtil;

import javax.annotation.PostConstruct;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * 存储应用程序的上下文信息
 *
 * <AUTHOR>
 */
public class MDCFilter implements Filter {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    /**
     * 要求パス
     */
    public static final String REQUESTURI = "REQUESTURI";

    /**
     * アプリ名
     */
    public static final String APNAME = "APNAME";

    /**
     * クライアントIP
     */
    public static final String HOST = "HOST";

    private EnvironmentConstant environmentConstant;

    /**
     * ユーザ情報
     */
    public static final String AUTHENTICATION = "AUTHENTICATION";

    /**
     * クライアントID
     */
    public static final String CLIENT_ID = "CLIENTID";

    /**
     * 移动端存放
     */
    public static final String USER_ID = "USER_ID";


    /**
     * クライアントID
     */
    public static final String CLIENT_TYPE = "CLIENT_TYPE";
    /**
     * クライアントID
     */
    public static final String STORE_ID = "STORE_ID";

    /**
     * クライアントID
     */
    public static final String TIME_STAMP = "Timestamp";


    List<SysClientType> sysClientTypes;

    public MDCFilter(EnvironmentConstant environmentConstant, List<SysClientType> sysClientTypes) {
        this.environmentConstant = environmentConstant;
        this.sysClientTypes = sysClientTypes;
    }

    @PostConstruct
    public void init() {

    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;

        checkAuthentication();

        MDC.put(APNAME, environmentConstant.getName());
        String idAddress = ConnectionUtil.getRemoteIp(req);
        if (StringUtils.isBlank(idAddress)) {
            logger.info("ip info lose,access forbidden.");
            return;
        }
        MDC.put(HOST, idAddress);
        MDC.put(REQUESTURI, req.getRequestURI());
        MDC.put(CLIENT_ID, req.getHeader(HeaderContent.HEAD_CLIENT_ID));
        MDC.put(CLIENT_TYPE, getUserAgent(req));
        MDC.put(STORE_ID, getStoreId(req));

        try {
            chain.doFilter(request, response);
        } finally {
            MDC.remove(HOST);
            MDC.remove(AUTHENTICATION);
            MDC.remove(APNAME);
            MDC.remove(CLIENT_ID);
            MDC.remove(REQUESTURI);
            MDC.remove(CLIENT_TYPE);
            MDC.remove(USER_ID);
            MDC.remove(STORE_ID);
        }
    }

    @Override
    public void destroy() {

    }

    public String getStoreId(HttpServletRequest request) {
        String storeId = request.getHeader(HeaderContent.HEADER_STORE_ID);
        return storeId == null ? "" : storeId;
    }

    public String getUserAgent(HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        String agent = "0";
        if (!CollectionUtils.isEmpty(sysClientTypes)) {
            for (SysClientType item : sysClientTypes) {
                if (userAgent.contains(item.getTypeName())) {
                    agent = String.valueOf(item.getId());
                    break;
                }
            }

        }
        return agent;
    }

    /**
     * ユーザ情報のチェック
     */
    public void checkAuthentication() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null == authentication || "anonymousUser" == authentication.getName()) {
            MDC.put(AUTHENTICATION, "DEFAULT");
        } else {
            MDC.put(AUTHENTICATION, authentication.getName());
        }
    }
}
