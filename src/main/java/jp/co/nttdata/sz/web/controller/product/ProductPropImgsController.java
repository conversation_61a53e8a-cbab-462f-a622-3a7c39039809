package jp.co.nttdata.sz.web.controller.product;

import io.micrometer.core.lang.Nullable;
import jp.co.nttdata.sz.web.service.product.IProductPropImgsService;
import jp.co.nttdata.sz.web.service.product.dto.ProductPropImgs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.constants.HeaderContent;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.fw.model.ServerRequestJsonBean;
import org.thanos.platform.util.PageUtils;
import org.thanos.platform.util.ResultDataUtil;
import org.thanos.platform.util.VaildErrorUtil;

import java.util.List;

/**
 * productController
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/imgs")
public class ProductPropImgsController {
    @Autowired
    private IProductPropImgsService productPropImgsService;

    @Autowired
    private LocaleMessageSourceService msgService;

    /**
     * 查询product列表
     */
    @GetMapping("/list")
    public ClientResponseJsonBean list(ProductPropImgs productPropImgs, @Nullable @RequestHeader(HeaderContent.HEADER_STORE_ID) String storeId) {
        PageUtils.startPage();
        ClientResponseJsonBean response = null;
        //判断校验是否有误
//        if (!result.hasErrors()) {
            productPropImgs.setBelongStore(storeId);
            List<ProductPropImgs> outputList = productPropImgsService.selectProductPropImgsList(productPropImgs);
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                    msgService.getMessage(MessageCode.SCOM00001), outputList);
//        } else {
//            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
//                    msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
//        }
        return response;
    }

//    /**
//     * 导出product列表
//     */
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ProductPropImgs productPropImgs)
//    {
//        List<ProductPropImgs> list = productPropImgsService.selectProductPropImgsList(productPropImgs);
//        ExcelUtil<ProductPropImgs> util = new ExcelUtil<ProductPropImgs>(ProductPropImgs.class);
//        util.exportExcel(response, list, "product数据");
//    }

    /**
     * 获取product详细信息
     */
    @GetMapping(value = "/{propImgId}")
    public ClientResponseJsonBean getInfo(@PathVariable("propImgId") Long propImgId, @Nullable @RequestHeader(HeaderContent.HEADER_STORE_ID) String storeId) {
        return ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                msgService.getMessage(MessageCode.SCOM00001),
                productPropImgsService.selectProductPropImgsByPropImgId(propImgId,storeId));
    }

    /**
     * 新增product
     */
    @PostMapping
    public ClientResponseJsonBean add(@RequestBody ProductPropImgs productPropImgs, @Nullable @RequestHeader(HeaderContent.HEADER_STORE_ID) String storeId) {
        productPropImgs.setBelongStore(storeId);
        int result = productPropImgsService.insertProductPropImgs(productPropImgs);
        ClientResponseJsonBean response = null;
        //判断校验是否有误
        if (result == 1) {
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                    msgService.getMessage(MessageCode.SCOM00001), result);
        } else {
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
                    msgService.getMessage(MessageCode.SCOM10205));
        }
        return response;
    }

    /**
     * 修改product
     */
    @PutMapping
    public ClientResponseJsonBean edit(@RequestBody ProductPropImgs productPropImgs, @Nullable @RequestHeader(HeaderContent.HEADER_STORE_ID) String storeId) {
        productPropImgs.setBelongStore(storeId);
        int result = productPropImgsService.updateProductPropImgs(productPropImgs);
        ClientResponseJsonBean response = null;
        //判断校验是否有误
        if (result == 1) {
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                    msgService.getMessage(MessageCode.SCOM00001), result);
        } else {
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
                    msgService.getMessage(MessageCode.SCOM10205));
        }
        return response;
    }

    /**
     * 删除product
     */
    @DeleteMapping("/{propImgIds}")
    public ClientResponseJsonBean remove(@PathVariable Long[] propImgIds, @Nullable @RequestHeader(HeaderContent.HEADER_STORE_ID) String storeId) {
        int result = productPropImgsService.deleteProductPropImgsByPropImgIds(propImgIds,storeId);
        ClientResponseJsonBean response = null;
        if (result > 0) {
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                    msgService.getMessage(MessageCode.SCOM00001), result);
        } else {
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
                    msgService.getMessage(MessageCode.SCOM10205));
        }
        return response;
    }
//    /**
//     * 导入product列表
//     */
//    @PostMapping("/import")
//    public AjaxResult importProductPropImgsList(MultipartFile file, boolean updateSupport) throws Exception
//    {
//        String message = productPropImgsService.importProductPropImgsList(file, updateSupport);
//        return AjaxResult.success(message);
//    }
//
//    /**
//     * 导入模板
//    */
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<ProductPropImgsImport> util = new ExcelUtil<ProductPropImgsImport>(ProductPropImgsImport.class);
//        util.importTemplateExcel(response, "Sheet1");
//    }
}
