package jp.co.nttdata.sz.web.service.iosuser;

import jp.co.nttdata.sz.web.controller.iosuser.dto.*;
import jp.co.nttdata.sz.web.entity.MemberEntity;
import jp.co.nttdata.sz.web.service.iosuser.dto.UserRegitserInputDto;

import java.util.List;

/**
 * ios手机用户用Service
 * <AUTHOR>
 *
 */
public interface IosUserService {
	
	/**
	 * 注册会员
	 * @param inputDto 入力会员信息
	 * @return 操作影响条数
	 */
	public int registerUser(UserRegitserInputDto inputDto);
	
	/**
	 * 刷新二维码
	 * @return
	 */
	public String refreshQrCode(IosUserScanerInptDto inptDto);

	/**
	 *	获取会员信息
	 * @return 会员信息
	 */
	public MemberEntity getMemberInfo();
	
	/**
	 * 手机端会员信息修改
	 * @param inputDto  入力会员修改信息
	 * @return 操作影响条数
	 */
	public String updateUser(IosMemberUpdateInputDto inputDto );
	
	/**
	 * 会员建议反馈
	 * @param inputDto
	 * @return
	 */
	public int memberSuggestion(IosMemberSuggestionInputDto inputDto);

	/**
	 * 获取推荐列表
	 * @return
	 */
	public List<IosSuggestProductOutputDto> getSuggestProductList(String storeId);

	/**
	 * 获取推荐列表
	 * @return
	 */
	public IosPlacardOutputDto getPlacard(String storeId);

	public int checkUserScanInput(IosUserScanerInptDto inputDto);

	public int deleteUserAccount(String userId);
}
