package jp.co.nttdata.sz.web.controller.user;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.auth.damain.SysWhiteTable;
import org.thanos.platform.fw.auth.service.SysWhiteTableService;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.fw.model.ServerRequestJsonBean;
import org.thanos.platform.util.ResultDataUtil;
import org.thanos.platform.util.VaildErrorUtil;

import javax.validation.Valid;

/**
 * Created by cong.ding on 2020/04/16.
 */
@Controller
@RequestMapping({"/system/controller"})
@Api(value = "系统管理", tags = {"系统管理Controller"})
public class SystemApiController {
    @Autowired
    private LocaleMessageSourceService msgConstant;

    @Autowired
    SysWhiteTableService sysWhiteTableService;

    @ResponseBody
    @PostMapping("addWhiteTable")
    @ApiOperation(value = "白名单信息添加", httpMethod = "POST")
    public ClientResponseJsonBean addwhiteTable(@Valid @RequestBody ServerRequestJsonBean<SysWhiteTable> inputDto,
                                                BindingResult result) {
        ClientResponseJsonBean response;
        // 入力check成功时
        if (!result.hasErrors()) {
            // 更新用户角色信息
            int i = sysWhiteTableService.insert(inputDto.getData());
            //返回数据设定
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                    msgConstant.getMessage(MessageCode.SCOM00001), i);

            // 入力check失败时
        } else {
            //返回信息设定SCOM10205
            response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
                    msgConstant.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
        }
        return response;
    }
}
