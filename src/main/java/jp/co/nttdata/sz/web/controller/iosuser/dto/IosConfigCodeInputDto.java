package jp.co.nttdata.sz.web.controller.iosuser.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class IosConfigCodeInputDto {

	@NotNull(message="configCode can not be null")
	@NotEmpty(message="configCode can not be empty")
	private String configCode;

	public String getConfigCode() {
		return configCode;
	}

	public void setConfigCode(String configCode) {
		this.configCode = configCode;
	}
	
}
