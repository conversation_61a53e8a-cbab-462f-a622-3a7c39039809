package jp.co.nttdata.sz.web.service.evaluation.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jp.co.nttdata.sz.web.controller.evaluation.dto.EvaluationInputDto;
import jp.co.nttdata.sz.web.controller.evaluation.dto.EvaluationOrderInfo;
import jp.co.nttdata.sz.web.controller.evaluation.dto.EvaluationOutputDto;
import jp.co.nttdata.sz.web.controller.evaluation.dto.EvaluationRealInfoDto;
import jp.co.nttdata.sz.web.dao.OrderEvaluationDao;
import jp.co.nttdata.sz.web.dao.OrderItemsDao;
import jp.co.nttdata.sz.web.entity.OrderEvaluation;
import jp.co.nttdata.sz.web.entity.OrderItem;
import jp.co.nttdata.sz.web.service.evaluation.EvaluationService;

/**
 * 订单评价ServiceImpl
 * 
 * <AUTHOR>
 *
 */
@Service
public class EvaluationServiceImpl implements EvaluationService {

	@Autowired
	private OrderEvaluationDao orderEvaluationDao;
	
	@Autowired
	private OrderItemsDao orderItemsDao;

	@Override
	public List<EvaluationOutputDto> searchEvaluation(EvaluationInputDto evaluationInputDto) {
		//	返回结果
		List<EvaluationOutputDto> evaluationList = new ArrayList<EvaluationOutputDto>();
		
		//	查询参数
		OrderEvaluation orderEvaluationInput = new OrderEvaluation();
		//	查询结果
		List<OrderEvaluation> orderEvaluationOutputList = new ArrayList<OrderEvaluation>();

		//	装填查询参数
		orderEvaluationInput.setUserAccount(evaluationInputDto.getUserAccount());
		orderEvaluationInput.setOrderId(evaluationInputDto.getOrderId());
		String timeString = evaluationInputDto.getEvaluationTime();
		if(timeString != null && !"".equals(timeString)) {
			String pattonString = "yyyy-MM-dd HH:mm:ss";
			if(!timeString.contains(":")) {
				timeString = timeString + " 12:12:12";
			}
			DateTimeFormatter df = DateTimeFormatter.ofPattern(pattonString);
			LocalDateTime evaluationLocalDateTime = LocalDateTime.parse(timeString,df);
			orderEvaluationInput.setEvaluationTime(evaluationLocalDateTime);
		}
		
		//	查询订单评价
		orderEvaluationOutputList = orderEvaluationDao.searchEvaluation(orderEvaluationInput);

		if (orderEvaluationOutputList != null && !orderEvaluationOutputList.isEmpty()) {
			// 装填评价信息
			for (OrderEvaluation orderEvaluation : orderEvaluationOutputList) {
				EvaluationOutputDto evaluation = new EvaluationOutputDto();

				evaluation.setOrderId(orderEvaluation.getOrderId());
				evaluation.setUserAccount(orderEvaluation.getUserAccount());
				evaluation.setCreateTime(orderEvaluation.getCreateTime());
				evaluation.setOrderEvaluation(orderEvaluation.getOrderEvaluation());
				evaluation.setEvaluationStar(orderEvaluation.getEvaluationStar());
				evaluation.setEvaluationTime(orderEvaluation.getEvaluationTime());
				evaluation.setStoreName(orderEvaluation.getStoreName());

				evaluationList.add(evaluation);
			}
		}

		return evaluationList;
	}

	@Override
	public List<EvaluationOutputDto> getEvaluation() {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public List<EvaluationOutputDto> getEvaluationByDate(String dateString) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public EvaluationRealInfoDto getEvaluationByOrderId(String orderId) {
		EvaluationRealInfoDto evaluationAllInfo =  new EvaluationRealInfoDto();
		EvaluationOrderInfo orderInfo = orderEvaluationDao.getOrderEvaluationByOrderId(orderId);
		if(orderInfo!=null) {
		List<OrderItem> orderItems = orderItemsDao.getOrderItemByOrderId(orderId);
		evaluationAllInfo.setOrderInfo(orderInfo);
		evaluationAllInfo.setOrderItems(orderItems);
		}
		return evaluationAllInfo;
	}

	@Override
	public List<EvaluationOutputDto> getEvaluationByUserName(String userName) {
		// TODO Auto-generated method stub
		return null;
	}

}
