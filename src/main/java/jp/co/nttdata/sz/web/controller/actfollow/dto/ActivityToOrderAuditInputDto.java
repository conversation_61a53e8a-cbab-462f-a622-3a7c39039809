package jp.co.nttdata.sz.web.controller.actfollow.dto;

import javax.validation.constraints.NotNull;

public class ActivityToOrderAuditInputDto {

	@NotNull(message="activityId can not be null")
	private  String activityId;
	
	@NotNull(message="orderId can not be null")
	private String orderId;
	
	@NotNull(message="isAgree can not be null")
	private Integer isAgree;

	public String getActivityId() {
		return activityId;
	}

	public String getOrderId() {
		return orderId;
	}

	public Integer getIsAgree() {
		return isAgree;
	}

	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public void setIsAgree(Integer isAgree) {
		this.isAgree = isAgree;
	}
	
	
	
}
