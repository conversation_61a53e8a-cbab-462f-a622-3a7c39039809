package jp.co.nttdata.sz.web.controller.weixin.dto;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

public class WeixinReturnOrderWrapperDto {

	@NotNull(message="orderId can not be null")
	private String orderId;
	
	@Valid
	@NotNull(message="returnItems can not be null")
	private List<WeixinReturnOrderItem> returnItems;
	
	private List<String> reasonPics;
	
	private String description;

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public List<WeixinReturnOrderItem> getReturnItems() {
		return returnItems;
	}

	public void setReturnItems(List<WeixinReturnOrderItem> returnItems) {
		this.returnItems = returnItems;
	}

	public List<String> getReasonPics() {
		return reasonPics;
	}

	public void setReasonPics(List<String> reasonPics) {
		this.reasonPics = reasonPics;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	
	
	
	
}
