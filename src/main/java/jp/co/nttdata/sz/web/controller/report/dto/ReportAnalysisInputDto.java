package jp.co.nttdata.sz.web.controller.report.dto;

import javax.validation.constraints.NotNull;

public class ReportAnalysisInputDto {

	@NotNull(message="storeId can not be null")
	private String storeId;

	private String type;

	private String productId;

	private String category;

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}
}
