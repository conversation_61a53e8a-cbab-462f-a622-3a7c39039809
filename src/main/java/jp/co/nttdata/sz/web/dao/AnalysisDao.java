package jp.co.nttdata.sz.web.dao;

import java.util.List;

import jp.co.nttdata.sz.web.controller.analysis.dto.OrderInfoInput;
import jp.co.nttdata.sz.web.controller.analysis.dto.PassengerFlowInput;
import jp.co.nttdata.sz.web.controller.analysis.dto.ShoppingInfoInput;
import jp.co.nttdata.sz.web.entity.OrderInfoEntity;
import jp.co.nttdata.sz.web.entity.PassengerFlowEntity;
import jp.co.nttdata.sz.web.entity.ProductInfo;
import jp.co.nttdata.sz.web.entity.ProductInfoEntity;
import jp.co.nttdata.sz.web.entity.ShoppingInfoEntity;
import jp.co.nttdata.sz.web.service.analysis.dto.StoreUserDataDto;

public interface AnalysisDao {
	
	/**
	 * 获取商品信息
	 * @return
	 */
	public List<ProductInfoEntity> getproductInfo();
	
	/**
	 * 获取某段时间内客流量
	 * @return
	 */
	public List<PassengerFlowEntity> getPassengerFlowByDay(PassengerFlowInput passengerFlowInput);
	
	/**
	 * 获取当天客流量
	 * @return
	 */
	public List<PassengerFlowEntity> getPassengerFlowByHour(PassengerFlowInput passengerFlowInput);
	public List<PassengerFlowEntity> getPassengerFlowByMonth(PassengerFlowInput passengerFlowInput);
	
	/**
	 * 获取商品库存信息
	 * @return
	 */
	public List<ProductInfo> getProductStockInfo();
	
	/**
	 * 获取进店人数
	 * @param shoppingInfoInput
	 * @return
	 */
	public ShoppingInfoEntity getIntoShopPeoCount(ShoppingInfoInput shoppingInfoInput);
	
	/**
	 * 获取购物信息
	 * @param shoppingInfoInput
	 * @return
	 */
	public ShoppingInfoEntity getShoppingInfo(ShoppingInfoInput shoppingInfoInput);

	/**
	 * 获取昨日/上周/上月销售额
	 * @param shoppingInfoInput
	 * @return
	 */
	public int getLastShoppingInfo(ShoppingInfoInput shoppingInfoInput);
	
	/**
	 * 获取一段时间内的订单信息
	 * @param shoppingInfoInput
	 * @return
	 */
	public List<OrderInfoEntity> getOrderInfo(OrderInfoInput orderInfoInput);
	public List<OrderInfoEntity> getOrderInfoWeek(OrderInfoInput orderInfoInput);
	public List<OrderInfoEntity> getOrderInfoMonth(OrderInfoInput orderInfoInput);
	
	/**
	 * 统计店铺内的会员消费数据
	 * @param consumerStoreId
	 * @return
	 */
	public List<StoreUserDataDto> selectUserStoreData(String consumerStoreId);
	
	

}
