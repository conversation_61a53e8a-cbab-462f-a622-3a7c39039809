package jp.co.nttdata.sz.web.controller.order.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class OrderIdInputDto {

	@NotNull(message="orderId can not be null")
	@NotEmpty(message="orderId can not be empty")
	private String orderId;

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
}
