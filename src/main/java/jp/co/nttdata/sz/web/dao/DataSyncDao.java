package jp.co.nttdata.sz.web.dao;


import jp.co.nttdata.sz.web.service.datasync.dto.*;

import java.util.List;

public interface DataSyncDao {

    //	删除货架备份信息
    int deleteShelfDataBk(String storeId);

    //	备份货架信息
    int backupShelfData(String storeId);

    //	删除货架信息
    int deleteShelfData(String storeId);

    int resetShelfStatus(String storeId);

    int resetLayerStatus(String storeId);

    int resetCargoStatus(String storeId);

    int resetCargoThingStatus(String storeId);

    int deleteShelfBatch(String storeId);

    int deleteLayerBatch(String storeId);

    int deleteCargoBatch(String storeId);

    int deleteCargoThingBatch(String storeId);

    //	插入货架信息
    int updateShelfData(ShelfData shelfData);

    //	删除货架层备份信息
    int deleteShelfLayerBK(String storeId);

    //	备份货架层信息
    int backupShelfLayer(String storeId);

    //	删除货架层信息
    int deleteShelfLayer(String storeId);

    //	插入货架层信息
    int updateShelfLayer(ShelfLayer shelfLayer);

    //	删除货道备份信息
    int deleteCargoRoadsBK(String storeId);

    //	备份货道信息
    int backupCargoRoads(String storeId);

    //	删除货道信息
    int deleteCargoRoads(String storeId);

    //	插入货道信息
    int updateCargoRoads(CargoRoads cargoRoads);

    //	删除货道商品备份信息
    int deleteCargoThingsBK(String storeId);

    //	备份货道商品信息
    int backupCargoThings(String storeId);

    //	删除货道商品信息
    int deleteCargoThings(String storeId);

    //	插入货道商品信息
    int updateCargoThings(CargoThings cargoThings);

    //	删除商品备份信息
    int deleteProductsBK(String storeId);

    //	备份商品信息
    int backupProducts(String storeId);

    //	删除商品信息
    int deleteProducts(String storeId);

    int resetProductStatus(String storeId);

    int resetSkuStatus(String storeId);

    int resetProductImageStatus(String storeId);

    int deleteProductBatch(String storeId);

    int deleteSkuBatch(String storeId);

    int deleteProductImageBatch(String storeId);

    //	插入商品信息
    int updateProducts(Items items);

    //	删除商品库存备份信息
    int deleteProductsSkusBK(String storeId);

    //	备份商品库存信息
    int backupProductsSkus(String storeId);

    //	删除商品库存信息
    int deleteProductsSkus(String storeId);

    //	插入商品库存信息
    int updateProductsSkus(Skus skus);

    //	删除商品图片备份信息
    int deleteProductImgBK(String storeId);

    //	备份商品图片信息
    int backupProductImg(String storeId);

    //	删除商品图片信息
    int deleteProductImg(String storeId);

    //	插入商品图片信息
    int updateProductImg(Propimgs propimgs);

    List<ShopMatchInfo> findShopThingInfo(SelfInfo selfInfo);

    ShopMatchInfo findThingBySukId(int sukId);

    int returnAllMisplaced();
}
