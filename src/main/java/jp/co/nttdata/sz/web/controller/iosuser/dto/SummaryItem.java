package jp.co.nttdata.sz.web.controller.iosuser.dto;

import java.math.BigDecimal;

public class SummaryItem {
    /**
     * 时间
     */
    private int name;

    /**
     * 消费金额
     */
    private BigDecimal amount;

    /**
     * 优惠金额
     */
    private BigDecimal discount;

    /**
     * 订单数
     */
    private Integer orderCount;

    private String userId;

    private int year;

    private int month;

    private int day;

    private int payStatus;

    public int getName() {
        return name;
    }

    public void setName(int name) {
        this.name = name;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(int payStatus) {
        this.payStatus = payStatus;
    }
}
