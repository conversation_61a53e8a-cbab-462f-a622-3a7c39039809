package jp.co.nttdata.sz.web.service.datasync.dto;

import java.util.List;

public class PointRule {
	private List<RedeemRule> RedeemRule;
	private List<AwardRule> AwardRule;
	public List<RedeemRule> getRedeemRule() {
		return RedeemRule;
	}
	public void setRedeemRule(List<RedeemRule> redeemRule) {
		RedeemRule = redeemRule;
	}
	public List<AwardRule> getAwardRule() {
		return AwardRule;
	}
	public void setAwardRule(List<AwardRule> awardRule) {
		AwardRule = awardRule;
	}
}
