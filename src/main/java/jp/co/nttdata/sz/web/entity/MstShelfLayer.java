package jp.co.nttdata.sz.web.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 或夹层板对象 mst_shelf_layer
 * 
 * <AUTHOR>
 * @date 2021-09-23
 */
public class MstShelfLayer
{
    private static final long serialVersionUID = 1L;

    /** 层板编号 */
    private Integer layerId;

    /** 货架编号 */
    private Integer shelfId;

    /** 层板高度 */
    private Long layerHeight;

    /** z轴起始点 */
    private Long startPointZ;

    /** 索引 */
    private Integer index;

    /** 货道总数 */
    private Integer cargoRoadCount;

    /** 所属店铺 */
    private String belongStore;

    /** 所属店铺 */
    private Integer isUpdated;


    /** 货道信息信息 */
    private List<ShelfCargoRoad> shelfCargoRoadList;

    public void setLayerId(Integer layerId) 
    {
        this.layerId = layerId;
    }

    public Integer getLayerId() 
    {
        return layerId;
    }
    public void setShelfId(Integer shelfId) 
    {
        this.shelfId = shelfId;
    }

    public Integer getShelfId() 
    {
        return shelfId;
    }
    public void setLayerHeight(Long layerHeight) 
    {
        this.layerHeight = layerHeight;
    }

    public Long getLayerHeight() 
    {
        return layerHeight;
    }
    public void setStartPointZ(Long startPointZ) 
    {
        this.startPointZ = startPointZ;
    }

    public Long getStartPointZ() 
    {
        return startPointZ;
    }
    public void setIndex(Integer index) 
    {
        this.index = index;
    }

    public Integer getIndex() 
    {
        return index;
    }
    public void setCargoRoadCount(Integer cargoRoadCount) 
    {
        this.cargoRoadCount = cargoRoadCount;
    }

    public Integer getCargoRoadCount() 
    {
        return cargoRoadCount;
    }

    public List<ShelfCargoRoad> getShelfCargoRoadList()
    {
        return shelfCargoRoadList;
    }

    public void setShelfCargoRoadList(List<ShelfCargoRoad> shelfCargoRoadList)
    {
        this.shelfCargoRoadList = shelfCargoRoadList;
    }

    public String getBelongStore() {
        return belongStore;
    }

    public void setBelongStore(String belongStore) {
        this.belongStore = belongStore;
    }

    public Integer getIsUpdated() {
        return isUpdated;
    }

    public void setIsUpdated(Integer isUpdated) {
        this.isUpdated = isUpdated;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("layerId", getLayerId())
            .append("shelfId", getShelfId())
            .append("layerHeight", getLayerHeight())
            .append("startPointZ", getStartPointZ())
            .append("index", getIndex())
            .append("cargoRoadCount", getCargoRoadCount())
            .append("shelfCargoRoadList", getShelfCargoRoadList())
            .append("belongStore", getBelongStore())
            .append("isUpdated", getIsUpdated())
            .toString();
    }
}
