package jp.co.nttdata.sz.web.controller.weixin;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.fw.model.ServerRequestJsonBean;
import org.thanos.platform.util.ResultDataUtil;
import org.thanos.platform.util.StringUtil;
import org.thanos.platform.util.VaildErrorUtil;

import jp.co.nttdata.sz.web.controller.iosorder.dto.IosOrderEvaluateInputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinEvaluationProductInputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinOrderIdInputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinPushSuggestionInputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinQrcodeOutputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinRegisterFaceInputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinReturnOrderWrapperDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinUpdateUserInputDto;
import jp.co.nttdata.sz.web.controller.weixin.dto.WeixinUserAuthInputDto;
import jp.co.nttdata.sz.web.entity.MemberEntity;
import jp.co.nttdata.sz.web.service.iosuser.dto.OrderListDto;
import jp.co.nttdata.sz.web.service.weixin.WeixinAuthUserTokenDto;
import jp.co.nttdata.sz.web.service.weixin.WeixinUserOperationService;


/**
 * 微信用户用controller
 * <AUTHOR>
 *
 */
@RequestMapping(value="wx")
@RestController
public class WeixinUserController {
	

	/**
	 * msg
	 */
	@Autowired
	private LocaleMessageSourceService msgService;
	
	@Autowired
	private WeixinUserOperationService wxUserOperationService;
	
	private static final String  HEADER_ACCESS_TOKEN = "accessToken";
	
	/**
	 * 验证微信用户
	 * @param inputBean
	 * @param result
	 * @return
	 */
	@PostMapping(value="auth-user")
	public ClientResponseJsonBean authWeixinUser(
			@Valid @RequestBody ServerRequestJsonBean<WeixinUserAuthInputDto> inputBean,BindingResult result) {
		ClientResponseJsonBean response = null;
		if(!result.hasErrors()) {
			//d认证微信用户
			WeixinAuthUserTokenDto outData =  wxUserOperationService.authWeixinUser(inputBean.getData());
			//d认证判断
			if(outData!=null&&outData.getAccessToken()!=null) {
				//d认证成功
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BAUT00002,
						msgService.getMessage(MessageCode.BAUT00002),outData);
			} else {
				//d认证失败
				response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM30010,
						msgService.getMessage(MessageCode.SCOM30010));
			}
		} else {
			//d入力格式校验错误
			response = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		return response;
	}
	
	/**
	 * 刷新入店二维码
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="refresh-qrcode")
	public ClientResponseJsonBean refreshQrcode(HttpServletRequest request,HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		String base64Img = wxUserOperationService.refreshQrcode(request.getHeader(HEADER_ACCESS_TOKEN));
		if(!StringUtil.isEmpty(base64Img)) {
			WeixinQrcodeOutputDto outData = new WeixinQrcodeOutputDto();
			outData.setQrCodeImg(base64Img);
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BQRC00001,
					msgService.getMessage(MessageCode.BQRC00001),outData);
		}else {
			// d返回数据设定
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BQRC00002,
								msgService.getMessage(MessageCode.BQRC00002));
		}
		//d刷新Token
		refreshToken(request,response);
		return realResponse;
	}
	
	/**
	 * 取得用户订单
	 * @param currentPage
	 * @param lastId
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="list-order/{currentPage}/{lastId}")
	public ClientResponseJsonBean listUserOrder(
			@PathVariable("currentPage") Integer currentPage,@PathVariable("lastId") Integer lastId,
			HttpServletRequest request,HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		//d取得当前用户的订单
		List<OrderListDto>  outData = wxUserOperationService.getOrderList(
				request.getHeader(HEADER_ACCESS_TOKEN), currentPage, lastId);
		//d判空数据
		if(outData!=null&&outData.size()>0) {
		realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
				msgService.getMessage(MessageCode.SCOM00001),outData);
		} else {
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM10204,
					msgService.getMessage(MessageCode.SCOM10204));	
		}
		//d刷新Token
		refreshToken(request,response);
		return realResponse;
	}
	
	
	/**
	 * 执行订单假删除
	 * @param inputDto
	 * @param result
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="delete-order")
	public ClientResponseJsonBean deleteOrder(
			@Valid @RequestBody ServerRequestJsonBean<WeixinOrderIdInputDto> inputDto, BindingResult result,
			HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if(!result.hasErrors()) {
			int ret = wxUserOperationService.deleteOrder(inputDto.getData().getOrderId());
			if(ret==1) {
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
						msgService.getMessage(MessageCode.SCOM00001));
			} else {
				//	订单不存在
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20106,
						msgService.getMessage(MessageCode.BORD20106));
			}
		} else {
			//d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		//d刷新Token
		refreshToken(request,response);
		return realResponse;
	}
	
	/**
	 * 退货申请
	 * @param inputBean
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="return-item")
	public ClientResponseJsonBean returnOrderItem(
			@Valid @RequestBody ServerRequestJsonBean<WeixinReturnOrderWrapperDto> inputBean,BindingResult result,
			HttpServletRequest request,HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if(!result.hasErrors()) {
			int total = wxUserOperationService.returnOrder(inputBean.getData());
			if(total>0) {
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
						msgService.getMessage(MessageCode.SCOM00001));	
			} else {
				//	退货申请失败
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20108,
						msgService.getMessage(MessageCode.BORD20108));
			}
		} else {
			//d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		//d刷新Token
		refreshToken(request,response);
		return realResponse;
		
	}
	
	/**
	 * 订单评价
	 * @param inputDto
	 * @param result
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="evaluation-order")
	public ClientResponseJsonBean evaluationOrder(
			@Valid @RequestBody ServerRequestJsonBean<IosOrderEvaluateInputDto> inputDto,BindingResult result,
			HttpServletRequest request,HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if(!result.hasErrors()) {
			int ret = wxUserOperationService.evaluationOrder(inputDto.getData());
			if(ret == -1) {
				//	订单不存在
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20106,
						msgService.getMessage(MessageCode.BORD20106));
			}else if(ret == 0) {
				//	评价失败
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20107,
						msgService.getMessage(MessageCode.BORD20107));
			}else {
				//	评价成功
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BORD00005,
						msgService.getMessage(MessageCode.BORD00005), null);
			}
		}else {
			//d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		//d刷新Token
		refreshToken(request,response);
		return realResponse;
	}
	
	/**
	 * 订单明细取得
	 * @param inputDto
	 * @param result
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="order-info")
	public ClientResponseJsonBean getOrderInfo(
			@Valid @RequestBody ServerRequestJsonBean<WeixinOrderIdInputDto> inputDto, BindingResult result,
			HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if (!result.hasErrors()) {
			OrderListDto  outData = wxUserOperationService.getOrderInfo(inputDto.getData().getOrderId());
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
					msgService.getMessage(MessageCode.SCOM00001),outData);
		} else {
			// d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		// d刷新Token
		refreshToken(request, response);
		return realResponse;
	}
	
	/**
	 * 会员个人信息拉取
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value = "user-info")
	public ClientResponseJsonBean getUserInfo(HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		MemberEntity outData = wxUserOperationService.getUserInfo(request.getHeader(HEADER_ACCESS_TOKEN));
		if (outData != null) {
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
					msgService.getMessage(MessageCode.SCOM00001), outData);
		} else {
			// 个人信息取得失败
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10204,
					msgService.getMessage(MessageCode.SCOM10204));
		}
		// d刷新Token
		refreshToken(request, response);
		return realResponse;
	}

	
	/**
	 * 更新用户信息
	 * @return
	 */
	@PostMapping(value="update-user")
	public ClientResponseJsonBean updateUserInfo(@RequestBody ServerRequestJsonBean<WeixinUpdateUserInputDto> inputDto,
			HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		int ret = wxUserOperationService.updateUserInfo(inputDto.getData(), request.getHeader(HEADER_ACCESS_TOKEN));
		if(ret>0) {
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
					msgService.getMessage(MessageCode.SCOM00001));
		} else {
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.BORD00006,
					msgService.getMessage(MessageCode.BORD00006));
		}
		// d刷新Token
		refreshToken(request, response);
		return realResponse;
		
	}
	
	/**
	 * 注册人脸
	 * @return
	 */
	@PostMapping(value="register-face")
	public ClientResponseJsonBean registerFace(
			@Valid @RequestBody ServerRequestJsonBean<WeixinRegisterFaceInputDto> inputDto, BindingResult result,
			HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if (!result.hasErrors()) {
			int ret = wxUserOperationService.registerFace(inputDto.getData(),
					request.getHeader(HEADER_ACCESS_TOKEN));
			if (ret > 0) {
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
						msgService.getMessage(MessageCode.SCOM00001));
			} else if (ret == 0) {
				//	图片均保存失败时
				//	返回信息设定
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BFCR10001,
						msgService.getMessage(MessageCode.BFCR10001) + VaildErrorUtil.getErrorMessages(result));
			} else if (ret == -1) {
				//	调用北京API录入人脸失败时
				//	返回信息设定
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BFCR10002,
						msgService.getMessage(MessageCode.BFCR10002) + VaildErrorUtil.getErrorMessages(result));
			} else {
				//	图片信息录入数据库失败失败时
				//	返回信息设定
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BFCR10003,
						msgService.getMessage(MessageCode.BFCR10003) + VaildErrorUtil.getErrorMessages(result));
			}
		} else {
			// d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		// d刷新Token
		refreshToken(request, response);
		return realResponse;

	}
	
	/**
	 * 提交反馈
	 * @param inputDto
	 * @param result
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value="push-suggestion")
	public ClientResponseJsonBean pushSuggestion(
			@Valid @RequestBody ServerRequestJsonBean<WeixinPushSuggestionInputDto> inputDto,BindingResult result,
			HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if (!result.hasErrors()) {
			int ret = wxUserOperationService.pushSuggestion(inputDto.getData(), 
					request.getHeader(HEADER_ACCESS_TOKEN));
			if(ret>0) {
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
						msgService.getMessage(MessageCode.SCOM00001));
			} else {
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM10103,
						msgService.getMessage(MessageCode.SCOM10103));
			}
		} else {
			// d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		} 
		// d刷新Token
		refreshToken(request, response);
		return realResponse;
	}
	
	/**
	 * 评价订单项商品
	 * @param inputDto
	 * @param result
	 * @param request
	 * @param response
	 * @return
	 */
	@PostMapping(value = "evaluation-product")
	public ClientResponseJsonBean evaluationProduct(
			@Valid @RequestBody ServerRequestJsonBean<WeixinEvaluationProductInputDto> inputDto, BindingResult result,
			HttpServletRequest request, HttpServletResponse response) {
		ClientResponseJsonBean realResponse = null;
		if (!result.hasErrors()) {
			int ret = wxUserOperationService.evaluationProduct(inputDto.getData(), 
					request.getHeader(HEADER_ACCESS_TOKEN));
			if(ret>0) {
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
						msgService.getMessage(MessageCode.SCOM00001));
			} else {
				//	评价失败
				realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.BORD20107,
						msgService.getMessage(MessageCode.BORD20107));
			}
		}  else {
			// d入力格式校验错误
			realResponse = ResultDataUtil.getResponseData(ResultDataUtil.RT.NG, CodeConstant.SCOM10205,
					msgService.getMessage(MessageCode.SCOM10205) + VaildErrorUtil.getErrorMessages(result));
		}
		// d刷新Token
		refreshToken(request, response);
		return realResponse;
	}
	
	/**
	 * 刷新token
	 * @param request
	 * @param response
	 */
	private void refreshToken(HttpServletRequest request,HttpServletResponse response) {
		String currentToken = request.getHeader(HEADER_ACCESS_TOKEN);
		if(!StringUtil.isEmpty(currentToken)) {
			String newToken  = wxUserOperationService.getNewToken(currentToken);
			if(!StringUtil.isEmpty(newToken)) {
			response.setHeader(HEADER_ACCESS_TOKEN, newToken);
			}
		}
	}
}
