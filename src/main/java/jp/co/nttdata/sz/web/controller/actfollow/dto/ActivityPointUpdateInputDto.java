package jp.co.nttdata.sz.web.controller.actfollow.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class ActivityPointUpdateInputDto {

	/**
	 * 单项id
	 */
	@NotNull(message="itemId can not be null")
	private Integer itemId;
	/**
	 * 商品id
	 */
	@NotNull(message="productId can not be null")
	private Integer productId;
	
	/**
	 * 商品名
	 */
	@NotNull(message="productTitle can not be null")
	@NotEmpty(message="productTitle can not be empty")
	private String productTitle;
	
	/**
	 * 数量
	 */
	@NotNull(message="quantity can not be null")
	private Integer quantity;
	
	/**
	 * 单价
	 */
	@NotNull(message="price can not be null")
	private BigDecimal price;
	
	/**
	 * 商品图片url
	 */
	@NotNull(message="picUrl can not be null")
	@NotEmpty(message="picUrl can not be empty")
	private String picUrl;
	
	/**
	 * 百分比
	 */
	private double precent;
	
	private Integer shopcarId;

	public Integer getItemId() {
		return itemId;
	}

	public void setItemId(Integer itemId) {
		this.itemId = itemId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductTitle() {
		return productTitle;
	}

	public void setProductTitle(String productTitle) {
		this.productTitle = productTitle;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public double getPrecent() {
		return precent;
	}

	public void setPrecent(double precent) {
		this.precent = precent;
	}

	public Integer getShopcarId() {
		return shopcarId;
	}

	public void setShopcarId(Integer shopcarId) {
		this.shopcarId = shopcarId;
	}	

}
