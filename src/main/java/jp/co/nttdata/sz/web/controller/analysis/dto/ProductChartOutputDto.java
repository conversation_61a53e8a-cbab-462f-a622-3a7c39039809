package jp.co.nttdata.sz.web.controller.analysis.dto;

import java.util.List;

public class ProductChartOutputDto {

    private List<SalesOutputDto> sales;
    private List<StorageOutputDto> storage;
    private int totalCount;
    private int skuCount;
    private int totalSkuCount;

    public List<SalesOutputDto> getSales() {
        return sales;
    }

    public void setSales(List<SalesOutputDto> sales) {
        this.sales = sales;
    }

    public List<StorageOutputDto> getStorage() {
        return storage;
    }

    public void setStorage(List<StorageOutputDto> storage) {
        this.storage = storage;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(int skuCount) {
        this.skuCount = skuCount;
    }

    public int getTotalSkuCount() {
        return totalSkuCount;
    }

    public void setTotalSkuCount(int totalSkuCount) {
        this.totalSkuCount = totalSkuCount;
    }
}
