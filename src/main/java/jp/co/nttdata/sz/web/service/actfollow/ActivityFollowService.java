package jp.co.nttdata.sz.web.service.actfollow;

import java.util.List;

import jp.co.nttdata.sz.bj.service.dto.BJTrackOutputDto;
import jp.co.nttdata.sz.web.controller.actfollow.dto.ActivityListInputDto;
import jp.co.nttdata.sz.web.controller.actfollow.dto.ActivityListOutputDto;
import jp.co.nttdata.sz.web.controller.actfollow.dto.ActivityPointUpdateInputDto;
import jp.co.nttdata.sz.web.controller.actfollow.dto.ActivityToOrderAuditInputDto;
import jp.co.nttdata.sz.web.controller.actfollow.dto.NoticeActivityExceptionInputDto;
import jp.co.nttdata.sz.web.controller.batchuse.dto.ActivityFollowEndDto;
import jp.co.nttdata.sz.web.entity.ShopcarEntity;
import jp.co.nttdata.sz.web.service.actfollow.dto.ActivityFollowInfoDto;
import jp.co.nttdata.sz.web.service.actfollow.dto.ActivityWrapperOutputDto;
import jp.co.nttdata.sz.web.service.actfollow.dto.UserFollowInfoDto;

/**
 * 入店活动跟踪service
 * <AUTHOR>
 *
 */
public interface ActivityFollowService {
	
	/**
	 * 获取当前的活动
	 * @return
	 */
	public List<ActivityFollowInfoDto> getNowActivityFollow(String storeId);
	
	/**
	 * 跟踪活动
	 * @param dto
	 * @return
	 */
	public ActivityWrapperOutputDto followActivity(UserFollowInfoDto dto);
	
	/**
	 * 取得所有活动所有节点信息
	 * @param activityId
	 * @return
	 */
	public ActivityWrapperOutputDto getActivityItems(String activityId);

	/**
	 * 取得所有活动所有节点信息
	 * @param activityId
	 * @return
	 */
	public ActivityWrapperOutputDto getActivity(String activityId);

	/**
	 * 取得当前活动
	 * @param memberId
	 * @return
	 */
	public ActivityFollowInfoDto getNewActivity(String memberId);
	
	/**
	 * 取得单个节点
	 * @param eventId
	 * @param sessionId
	 * @return
	 */
	public ShopcarEntity getAcitivityItem(String eventId,String sessionId);
	
	/**
	 * 取得已经出店的活动
	 * @param memberId
	 * @return
	 */
	public ActivityFollowEndDto getEndActivity(String sessionId);
	
	/**
	 * 异常关闭/手动关闭 关闭活动跟踪
	 * @param activityId
	 * @return
	 */
	public int closeActivityFollow(String activityId);
	
	/**
	 * 活动手动确认
	 * @param activityId
	 * @return
	 */
	public int confirmActivityFollow(String activityId);
	
	public int endActivityFollow(String activityId);
	
	public int updateActivityPoint(ActivityPointUpdateInputDto inputDto);
	
	public int insertActivityPoint(ActivityPointUpdateInputDto inputDto);

	public List<ActivityListOutputDto> getActivityList(ActivityListInputDto inputDto);
	
	public BJTrackOutputDto getActTrack(String activityId);
	
	public int noticeActivityException(NoticeActivityExceptionInputDto inputDto);
	
	public int recoverExceptionActivity(String activityId);
	
	public int auditActivityOrder(ActivityToOrderAuditInputDto inputDto);
	
	public ActivityWrapperOutputDto getAuditOrder(String orderId);

}
