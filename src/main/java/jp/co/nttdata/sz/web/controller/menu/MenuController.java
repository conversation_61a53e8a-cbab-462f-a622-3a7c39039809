package jp.co.nttdata.sz.web.controller.menu;

import io.micrometer.core.lang.Nullable;
import jp.co.nttdata.sz.web.controller.menu.dto.MenuCountOutputDto;
import jp.co.nttdata.sz.web.service.menu.MenuCountService;
import jp.co.nttdata.sz.web.service.menu.MenuInfoEntityService;
import jp.co.nttdata.sz.web.service.menu.dto.MenuCountWrapperDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.thanos.core.message.LocaleMessageSourceService;
import org.thanos.core.message.MessageCode;
import org.thanos.platform.fw.constants.CodeConstant;
import org.thanos.platform.fw.constants.HeaderContent;
import org.thanos.platform.fw.model.ClientResponseJsonBean;
import org.thanos.platform.util.ResultDataUtil;

/**
 * Created by cong.ding on 2020/04/20.
 */
@Controller
@RequestMapping("/api/v1")
public class MenuController {

    @Autowired
    private LocaleMessageSourceService msgService;
    @Autowired
    MenuInfoEntityService menuInfoEntityService;
    @Autowired
    MenuCountService menuCountService;

    @GetMapping("menu")
    @ResponseBody
    public ClientResponseJsonBean getMenu(
            @Nullable @RequestHeader(HeaderContent.HEADER_LANGUAGE) String language) {
        // 返回信息设定
        return ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001,
                msgService.getMessage(MessageCode.SCOM00001), menuInfoEntityService.selectAll(language == null ? "zh" : language));
    }

    /**
     * getMenuCount方法
     */
    @PostMapping(value = "menu/count")
    @ResponseBody
    public ClientResponseJsonBean getMenuCount() {
        // 初始化响应数据
        ClientResponseJsonBean response = null;
        MenuCountOutputDto outData = new MenuCountOutputDto();
        // 获取menuCount数据
        MenuCountWrapperDto menuCountWrapperDto = menuCountService.getMenuCount();

        outData.setAuthstrCount(menuCountWrapperDto.getAuthstrCount());
        outData.setPendingOrderCount(menuCountWrapperDto.getPendingOrderCount());
        outData.setSalesReturnCount(menuCountWrapperDto.getSalesReturnCount());
        // 返回数据设定
        response =
                ResultDataUtil.getResponseData(ResultDataUtil.RT.OK, CodeConstant.SCOM00001, msgService.getMessage(MessageCode.SCOM00001), outData);
        return response;

    }
}
