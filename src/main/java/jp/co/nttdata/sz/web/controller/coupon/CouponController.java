package jp.co.nttdata.sz.web.controller.coupon;

import jp.co.nttdata.sz.web.entity.CouponEntity;
import jp.co.nttdata.sz.web.entity.CouponUserEntity;
import jp.co.nttdata.sz.web.service.coupon.CouponService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 红包Controller
 */
@RestController
@RequestMapping("/coupon")
public class CouponController {

    private static final Logger logger = LoggerFactory.getLogger(CouponController.class);

    @Autowired
    private CouponService couponService;

    /**
     * 创建红包
     */
    @PostMapping("/create")
    public Map<String, Object> createCoupon(@RequestBody CouponEntity coupon) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long id = couponService.createCoupon(coupon);
            result.put("success", true);
            result.put("msg", "创建成功");
            result.put("data", id);
        } catch (Exception e) {
            logger.error("创建红包失败", e);
            result.put("success", false);
            result.put("msg", "创建失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新红包
     */
    @PostMapping("/update")
    public Map<String, Object> updateCoupon(@RequestBody CouponEntity coupon) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = couponService.updateCoupon(coupon);
            result.put("success", success);
            result.put("msg", success ? "更新成功" : "更新失败");
        } catch (Exception e) {
            logger.error("更新红包失败", e);
            result.put("success", false);
            result.put("msg", "更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除红包
     */
    @PostMapping("/delete/{id}")
    public Map<String, Object> deleteCoupon(@PathVariable("id") Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = couponService.deleteCoupon(id);
            result.put("success", success);
            result.put("msg", success ? "删除成功" : "删除失败");
        } catch (Exception e) {
            logger.error("删除红包失败", e);
            result.put("success", false);
            result.put("msg", "删除失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取红包详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getCouponDetail(@PathVariable("id") Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            CouponEntity coupon = couponService.getCouponById(id);
            if (coupon != null) {
                result.put("success", true);
                result.put("data", coupon);
            } else {
                result.put("success", false);
                result.put("msg", "红包不存在");
            }
        } catch (Exception e) {
            logger.error("获取红包详情失败", e);
            result.put("success", false);
            result.put("msg", "获取详情失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取红包列表
     */
    @GetMapping("/list")
    public Map<String, Object> getCouponList(
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "couponType", required = false) Integer couponType,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("title", title);
            params.put("couponType", couponType);
            params.put("status", status);
            params.put("start", (page - 1) * limit);
            params.put("limit", limit);

            List<CouponEntity> list = couponService.getCouponList(params);
            int total = couponService.getCouponCount(params);

            result.put("success", true);
            result.put("data", list);
            result.put("total", total);
        } catch (Exception e) {
            logger.error("获取红包列表失败", e);
            result.put("success", false);
            result.put("msg", "获取列表失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新红包状态
     */
    @PostMapping("/status/{id}")
    public Map<String, Object> updateCouponStatus(
            @PathVariable("id") Long id,
            @RequestParam("status") int status) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = couponService.updateCouponStatus(id, status);
            result.put("success", success);
            result.put("msg", success ? "更新状态成功" : "更新状态失败");
        } catch (Exception e) {
            logger.error("更新红包状态失败", e);
            result.put("success", false);
            result.put("msg", "更新状态失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 向用户分发红包
     */
    @PostMapping("/distribute/{couponId}")
    public Map<String, Object> distributeCoupon(
            @PathVariable("couponId") Long couponId,
            @RequestBody List<String> userIds) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = couponService.distributeCouponToUsers(couponId, userIds);
            result.put("success", success);
            result.put("msg", success ? "分发成功" : "分发失败");
        } catch (Exception e) {
            logger.error("分发红包失败", e);
            result.put("success", false);
            result.put("msg", "分发失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取红包分发用户列表
     */
    @GetMapping("/users/{couponId}")
    public Map<String, Object> getCouponUserList(@PathVariable("couponId") Long couponId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<CouponUserEntity> list = couponService.getCouponUserList(couponId);
            result.put("success", true);
            result.put("data", list);
        } catch (Exception e) {
            logger.error("获取红包用户列表失败", e);
            result.put("success", false);
            result.put("msg", "获取列表失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户可用红包列表
     */
    @GetMapping("/user/{userId}")
    public Map<String, Object> getUserAvailableCoupons(@PathVariable("userId") String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<CouponUserEntity> list = couponService.getUserAvailableCoupons(userId);
            result.put("success", true);
            result.put("data", list);
        } catch (Exception e) {
            logger.error("获取用户红包列表失败", e);
            result.put("success", false);
            result.put("msg", "获取列表失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 使用红包
     */
    @PostMapping("/use")
    public Map<String, Object> useCoupon(
            @RequestParam("couponUserId") Long couponUserId,
            @RequestParam("orderId") String orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = couponService.useCoupon(couponUserId, orderId);
            result.put("success", success);
            result.put("msg", success ? "使用成功" : "使用失败");
        } catch (Exception e) {
            logger.error("使用红包失败", e);
            result.put("success", false);
            result.put("msg", "使用失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 取消使用红包
     */
    @PostMapping("/cancel")
    public Map<String, Object> cancelUseCoupon(@RequestParam("couponUserId") Long couponUserId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = couponService.cancelUseCoupon(couponUserId);
            result.put("success", success);
            result.put("msg", success ? "取消成功" : "取消失败");
        } catch (Exception e) {
            logger.error("取消使用红包失败", e);
            result.put("success", false);
            result.put("msg", "取消失败: " + e.getMessage());
        }
        return result;
    }
}
