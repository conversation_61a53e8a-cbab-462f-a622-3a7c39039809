/**
 * 
 */
package jp.co.nttdata.sz.web.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import jp.co.nttdata.sz.web.entity.ShopcarEntity;
import jp.co.nttdata.sz.web.service.orderopt.dto.OrderShopcarTimeDto;

/**
 * <AUTHOR>
 *
 */
public interface ShopcarDao {
	
	/**
	 * 根据id取得购物车信息
	 * @param shopcarId
	 * @return
	 */
	List<ShopcarEntity> getShopCarById(Integer shopcarId);
	
	List<OrderShopcarTimeDto> getShopCarByIdToList(Integer shopcarId);
	
	int delShopcarById(Integer shopcarId);
	
	int delShopcarItemsById(Integer shopcarId);
	
	ShopcarEntity getShopcarItem(@Param("eventId") String eventId,@Param("sessionId") String sessionId);
	
}
