package jp.co.nttdata.sz.web.controller.member.dto;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 会员审核用入力dto
 * <AUTHOR>
 *
 */
public class MemberCheckInputDto {

	/**
	 * 会员店铺id
	 */
	@NotNull(message="userStoreId can not be null")
	@NotEmpty(message="userStoreId can not be empty")
	private List<String> userStoreId;

	public List<String> getUserStoreId() {
		return userStoreId;
	}

	public void setUserStoreId(List<String> userStoreId) {
		this.userStoreId = userStoreId;
	}
}
