package jp.co.nttdata.sz.cs.controller.dto;

public enum SysGroupEnum {
        ADMIN("系统管理员", "1"), USER("ios用户组", "3"), SHOPOWNER("店长组", "4"), SHOPSTAFF("店铺职员组", "5"), TESTER("测试人员组", "6"), TOURIST("游客组", "7"), SHOPMANAGER("店铺权限组", "9");

    private String name;
    private String value;

    private SysGroupEnum(String name, String value) {
        this.setName(name);
        this.setValue(value);
    }

    public static SysGroupEnum getSysGroupByValue(String value) {
        for (SysGroupEnum sysGroup : values()) {
            if (sysGroup.getValue().equals(value)) {
                return sysGroup;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
