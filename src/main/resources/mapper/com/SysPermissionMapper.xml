<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.thanos.platform.fw.auth.dao.SysPermissionDao">

    <resultMap id="result"
               type="org.thanos.platform.fw.auth.damain.SysPermission">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="INTEGER"/>
        <result column="permission" property="permission"
                jdbcType="VARCHAR"/>
        <result column="flag_name" property="flagName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listByRoleId" parameterType="java.lang.Integer"
            resultMap="result">
        SELECT * FROM sys_permission WHERE role_id=#{roleId}
    </select>


    <select id="selectAll" resultMap="result">
        SELECT *
        FROM sys_permission
        where enabled=1
    </select>

    <insert id="insert" parameterType="org.thanos.platform.fw.auth.damain.SysPermission">
        INSERT INTO  sys_permission (url,role_id,permission) VALUES ( #{url},#{roleId}, #{permission});
    </insert>

    <resultMap id="SysPermission" type="org.thanos.platform.fw.auth.damain.SysPermission">
    <result property="id" column="id" jdbcType="NUMERIC" />
    <result property="url" column="url"
            jdbcType="VARCHAR" />
    <result property="roleId" column="role_id"
            jdbcType="VARCHAR" />
    <result property="roleName" column="role_name" jdbcType="VARCHAR" />
    <result property="permission" column="permission" jdbcType="VARCHAR" />
        <result property="enabled" column="enabled" jdbcType="VARCHAR" />
    </resultMap>


    <select id="selectAllPermission" resultMap="SysPermission">
        SELECT
        t1.id,
        t1.url,
        t1.role_id,
        t2.role_name,
        t1.permission,
        t1.enabled
        FROM
        sys_permission t1,
        sys_role t2
        WHERE
        t1.role_id = t2.role_id
    </select>

    <delete id="delPermission" parameterType="java.lang.Integer">
        UPDATE sys_permission
        SET enabled = 0
        WHERE
        role_id= #{roleId}
    </delete>

    <insert id="insertPermission" parameterType="jp.co.nttdata.sz.web.controller.permission.dto.InsertPermissionInputDto">
        INSERT INTO sys_permission ( url, role_id, permission, flag_name )
        VALUES
        ( #{url},#{roleId},#{permission},#{flagName} )
    </insert>

    <update id="updatePermission" parameterType="jp.co.nttdata.sz.web.controller.permission.dto.UpdatePermissionInputDto">
        UPDATE
        sys_permission
        SET
        role_id = #{roleId}
        <if test="url !=null">
           , url = #{url}
        </if>
        <if test="permission !=null">
            , permission = #{permission}
        </if>
        <if test="flagName !=null">
            , flag_name = #{flagName}
        </if>
        <if test="enabled !=null">
            , enabled = #{enabled}
        </if>
        WHERE
        role_id =
        #{roleId}


    </update>

</mapper>