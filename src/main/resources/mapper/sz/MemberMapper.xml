<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.MemberDao">

	<select id="getMemberInfoByUserId" parameterType="java.lang.String" resultType="jp.co.nttdata.sz.web.entity.MemberEntity">
	SELECT 
	  user_account AS userAccount,
	  user_name AS userName,
	  member_avatar AS memberAvatar,
	  user_phone AS userPhone,
	  t2.wx_group AS wxGroup,
	  SUM( IF ( t4.STATUS = 1, cast(t4.amount as decimal(11,2)), 0 ) ) AS amount,
	  SUM( IF ( t4.STATUS = 1, cast(t3.amount as decimal(11,2)) - cast(t3.prom_amount as decimal(11,2)), 0 ) ) AS discountAmount,
	  SUM( IF ( t4.STATUS = 0, cast(t4.amount as decimal(11,2)), 0 ) ) AS unpaidAmount
	FROM
	  sys_user  AS t1
	  LEFT JOIN wx_binding_user AS t2 
	  ON t1.user_id = t2.user_id
	  LEFT JOIN mst_order AS t3 ON t1.user_id = t3.user_id
	  LEFT JOIN order_settlement AS t4 ON t3.order_id = t4.order_id
		WHERE
	  t1.user_id = #{userId}
	GROUP BY
		user_account ,
		user_name ,
		member_avatar ,
		user_phone ,
		t2.wx_group
	</select>

	<update id="updateMemberInfo"
		parameterType="jp.co.nttdata.sz.web.controller.weixin.dto.WeixinUpdateUserInputDto">
		UPDATE sys_user
		AS t1 LEFT JOIN wx_binding_user AS t2
		ON t1.user_id = t2.user_id
		SET 
		t1.user_id = #{userId}
		<if test="userName!=null and userName !=''">
		,t1.user_name = #{userName}
		</if>
		<if test="userPhone!=null and userPhone !=''">
		,t1.user_phone = #{userPhone}
		</if>
		<if test="memberAvatar!=null and memberAvatar!=''">
		,t1.member_avatar =#{memberAvatar}
		</if>
		<if test="wxGroup!=null and wxGroup!=''">
		,t2.wx_group = #{wxGroup}
		</if>
		WHERE t1.user_id = #{userId}
	</update>

</mapper> 
