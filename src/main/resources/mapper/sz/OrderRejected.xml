<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.OrderRejectedDao">

    <sql id="columns"> id AS id, order_id AS orderId, rejected_reason AS rejectedReason,
        operate_user_id AS operateUserId, operate_user_name AS operateUserName, operate_time AS
        operateTime </sql>

    <!-- 根据订单ID查询订单驳回内容 -->
    <select id="getOrderRejectedByOrderId"
        resultType="jp.co.nttdata.sz.web.entity.OrderRejected"> SELECT <include refid="columns" />
        FROM order_rejected WHERE order_id = #{orderId} </select>


    <!-- 插入一条订单驳回 -->
    <insert id="insertOrderRejected"
        parameterType="jp.co.nttdata.sz.web.entity.OrderRejected"> INSERT INTO order_rejected
        (order_id,rejected_reason,operate_user_id,operate_user_name,operate_time) VALUES
        (#{orderId},#{rejectedReason},#{operateUserId},#{operateUserName},CURRENT_TIMESTAMP) </insert>
</mapper>