<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.MemberSuggestionDao">
	<resultMap id="memberSuggestion"
			   type="jp.co.nttdata.sz.web.entity.FeedbackEntity">
		<result property="id" column="id" jdbcType="INTEGER" />
		<result property="userName" column="user_name" jdbcType="VARCHAR"/>
		<result property="staffNumber" column="staff_number" jdbcType="VARCHAR"/>
		<result property="message" column="message" jdbcType="VARCHAR" />
		<result property="feedbackType" column="suggestion_type" jdbcType="VARCHAR" />
		<result property="feedbackTags" column="suggestion_tag" jdbcType="VARCHAR" />
		<result property="imagePath" column="suggestion_images" jdbcType="VARCHAR" />
		<result property="createTime" column="create_time" jdbcType="VARCHAR" />
	</resultMap>

	<insert id="insertSuggestion"
		parameterType="jp.co.nttdata.sz.web.entity.MemberSuggestionEntity">
		INSERT INTO ios_member_suggestion
		(member_id,suggestion_type,message,phone,suggestion_tag,suggestion_images,store_id)
		VALUES
		(#{memberId},#{suggestionType},#{message},#{phone},#{feedbackTags},#{imagePaths},#{storeId})
	</insert>

	<select id="selectSuggestList" parameterType="jp.co.nttdata.sz.web.controller.platform.dto.FeedbackInputDto" resultMap="memberSuggestion">
		select t1.*,t2.user_name,t2.staff_number from ios_member_suggestion t1
		left JOIN sys_user t2 on t2.user_id = t1.member_id
		where
		      1=1
		<if test="userName!=null and userName !=''">
			 AND t2.user_name LIKE concat('%', #{userName}, '%')
		</if>
		<if test="storeId !=null and storeId !=''">
			AND t1.store_id = #{storeId}
		</if>
		order by t1.create_time desc
	</select>

</mapper> 
