<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="jp.co.nttdata.sz.web.dao.SalesStatusDao">

    <select id="getTodayAmount" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(CAST(prom_amount AS DECIMAL(10,2))),0)
        FROM
            mst_order
        WHERE
            order_status IN ('5','6','8')
        AND consumer_store_id = #{storeId}
        AND DATE(create_time) = CURDATE() ;
    </select>

    <select id="getTodayRefundAmount" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(CAST(T1.return_amount AS DECIMAL(10,2))),0)
        FROM
            mst_sales_return T1
        INNER JOIN
            mst_order T2
        ON T2.order_id = T1.order_id
        WHERE
            T1.sales_retrun_status = #{inputDto.salesReturnStatus}
            AND T2.order_status = #{inputDto.orderStatus}
            AND T2.consumer_store_id = #{inputDto.storeId}
            AND DATE(T2.create_time) = CURDATE()
    </select>

    <select id="getYesterdayAmount" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(CAST(prom_amount AS DECIMAL(10,2))),0)
        FROM
            mst_order
        WHERE
            order_status IN ('5','6','8')
        AND consumer_store_id = #{storeId}
        AND DATE(create_time) = CURDATE() - INTERVAL 1 DAY;
    </select>

    <select id="getYesterdayRefundAmount" resultType="java.math.BigDecimal">
        SELECT
            COALESCE(SUM(CAST(T1.return_amount AS DECIMAL(10,2))),0)
        FROM
            mst_sales_return T1
                INNER JOIN
            mst_order T2
            ON T2.order_id = T1.order_id
        WHERE
            T1.sales_retrun_status = #{inputDto.salesReturnStatus}
            AND T2.order_status = #{inputDto.orderStatus}
            AND T2.consumer_store_id = #{inputDto.storeId}
            AND DATE(T2.create_time) = CURDATE() - INTERVAL 1 DAY;
    </select>

    <select id="humanCount" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COUNT(shopcar_id)
        FROM
            mst_shopcar
        WHERE
            store_id = #{infoDto.storeId}
          AND DATE(start_time) <![CDATA[>=]]> STR_TO_DATE(#{infoDto.weekStartDate},'%Y-%m-%d')
          AND DATE(end_time) <![CDATA[<=]]> STR_TO_DATE(#{infoDto.weekEndDate},'%Y-%m-%d')
    </select>


    <select id="itemCount" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COALESCE(SUM(quantity),0)
        FROM
            mst_order
        WHERE
            order_status IN ('5','6','8')
          AND consumer_store_id = #{infoDto.storeId}
          AND DATE(create_time) <![CDATA[>=]]> STR_TO_DATE(#{infoDto.weekStartDate},'%Y-%m-%d')
          AND DATE(create_time) <![CDATA[<=]]> STR_TO_DATE(#{infoDto.weekEndDate},'%Y-%m-%d')
    </select>

    <select id="itemReturnCount" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COALESCE(SUM(T1.quantity),0)
        FROM
            sales_return_items T1
        INNER JOIN
            mst_order T2
        ON T2.order_id = T1.sales_return_id
        WHERE
            T2.order_status = '5'
          AND T2.consumer_store_id = #{infoDto.storeId}
          AND DATE(T2.create_time) <![CDATA[>=]]> STR_TO_DATE(#{infoDto.weekStartDate},'%Y-%m-%d')
          AND DATE(T2.create_time) <![CDATA[<=]]> STR_TO_DATE(#{infoDto.weekEndDate},'%Y-%m-%d')
    </select>

    <select id="orderCount" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COUNT(order_id)
        FROM
            mst_order
        WHERE
            order_status IN ('5','6','8')
          AND consumer_store_id = #{infoDto.storeId}
          AND DATE(create_time) <![CDATA[>=]]> STR_TO_DATE(#{infoDto.weekStartDate},'%Y-%m-%d')
          AND DATE(create_time) <![CDATA[<=]]> STR_TO_DATE(#{infoDto.weekEndDate},'%Y-%m-%d')
    </select>


    <select id="humanCountHours" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COUNT(shopcar_id)
        FROM
            mst_shopcar
        WHERE
            store_id = #{infoDto.storeId}
          AND start_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL 24 HOUR)
          AND end_time <![CDATA[<=]]> NOW()
    </select>


    <select id="itemCountHours" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COALESCE(SUM(quantity),0)
        FROM
            mst_order
        WHERE
            order_status IN ('5','6','8')
          AND consumer_store_id = #{infoDto.storeId}
          AND create_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL 24 HOUR)
          AND create_time <![CDATA[<=]]> NOW()
    </select>

    <select id="itemReturnCountHours" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COALESCE(SUM(T1.quantity),0)
        FROM
            sales_return_items T1
        INNER JOIN
            mst_order T2
            ON T2.order_id = T1.sales_return_id
        WHERE
            T2.order_status = '5'
            AND T2.consumer_store_id = #{infoDto.storeId}
            AND T2.create_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL 24 HOUR)
            AND T2.create_time <![CDATA[<=]]> NOW()
    </select>

    <select id="orderCountHours" parameterType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesStatusInputDto"
            resultType="java.lang.Integer">
        SELECT
            COUNT(order_id)
        FROM
            mst_order
        WHERE
            order_status IN ('5','6','8')
          AND consumer_store_id = #{infoDto.storeId}
          AND create_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL 24 HOUR)
          AND create_time <![CDATA[<=]]> NOW()
    </select>

    <select id="weekSalesAmountInfo" resultType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto">
        WITH RECURSIVE DateRange AS (
            SELECT
                STR_TO_DATE(#{infoDto.weekStartDate}, '%Y-%m-%d') AS date
            UNION ALL
            SELECT
                DATE_ADD(date, INTERVAL 1 DAY)
            FROM
                DateRange
            WHERE
                DATE <![CDATA[<]]> STR_TO_DATE(#{infoDto.weekEndDate}, '%Y-%m-%d')
        )
        SELECT
            DATE_FORMAT(T1.date, '%m.%d') AS date
             , COALESCE(SUM(CAST(T2.prom_amount AS DECIMAL(10,2))), 0) AS amount
        FROM
            DateRange T1
                LEFT JOIN mst_order T2
                          ON DATE (T2.create_time) = T1.date
                              AND T2.order_status IN ('5','6','8')
                              AND T2.consumer_store_id = #{infoDto.storeId}
        GROUP BY
            T1.date
        ORDER BY
            T1.date
    </select>

    <select id="monthSalesAmountInfo" resultType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto">
        WITH RECURSIVE DateRange AS (
            SELECT
                STR_TO_DATE(#{infoDto.weekStartDate}, '%Y-%m-%d') AS date
            UNION ALL
            SELECT
                DATE_ADD(date, INTERVAL 1 DAY)
            FROM
                DateRange
            WHERE
                DATE <![CDATA[<]]> STR_TO_DATE(#{infoDto.weekEndDate}, '%Y-%m-%d')
        )
        SELECT
            DATE_FORMAT(MAX(T1.date), '%m.%d') AS date,
            COALESCE(SUM(CAST(T2.prom_amount AS DECIMAL(10,2))), 0) AS amount
        FROM
            DateRange T1
                LEFT JOIN mst_order T2
                          ON DATE(T2.create_time) = T1.date
                              AND T2.order_status IN ('5','6','8')
                              AND T2.consumer_store_id = #{infoDto.storeId}
        GROUP BY
            FLOOR(DATEDIFF(T1.date, STR_TO_DATE(#{infoDto.weekStartDate}, '%Y-%m-%d')) / 5)
        ORDER BY
            MAX(T1.date)
    </select>


        <select id="hoursSalesAmountInfo" resultType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto">
        WITH RECURSIVE HourRange AS (
            SELECT
                DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 23 HOUR), '%Y-%m-%d %H:00:00') AS hour_time
            UNION ALL
            SELECT
                DATE_FORMAT(DATE_ADD(STR_TO_DATE(hour_time, '%Y-%m-%d %H:%i:%s'), INTERVAL 1 HOUR), '%Y-%m-%d %H:00:00')
            FROM
                HourRange
            WHERE
                STR_TO_DATE(hour_time, '%Y-%m-%d %H:%i:%s') <![CDATA[<]]> NOW()
        )
        SELECT
            CONCAT(
                DATE_FORMAT(MIN(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s')), '%H:00')
            ) AS date,
            COALESCE(SUM(CAST(T2.prom_amount AS DECIMAL(10,2))), 0) AS amount
        FROM
            HourRange T1
            LEFT JOIN mst_order T2
                ON DATE_FORMAT(T2.create_time, '%Y-%m-%d %H') = DATE_FORMAT(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d %H')
                AND T2.order_status IN ('5','6','8')
                AND T2.consumer_store_id = #{infoDto.storeId}
        GROUP BY
            FLOOR(HOUR(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s')) / 4)
        ORDER BY
            MIN(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s'))
    </select>


    <select id="getWeekRefundAmount" resultType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto">
        SELECT
            DATE_FORMAT(T2.create_time, '%m-%d') AS date,
            COALESCE(SUM(CAST(T1.return_amount AS DECIMAL(10,2))), 0) as amount
        FROM
            mst_sales_return T1
        INNER JOIN
            mst_order T2
        ON T2.order_id = T1.order_id
        AND T2.order_status = #{inputDto.orderStatus}
        AND T2.consumer_store_id = #{inputDto.storeId}
        AND DATE(T2.create_time) <![CDATA[>=]]> STR_TO_DATE(#{inputDto.startDate}, '%Y-%m-%d')
        AND DATE(T2.create_time) <![CDATA[<=]]> STR_TO_DATE(#{inputDto.endDate},'%Y-%m-%d')
        WHERE
            T1.sales_retrun_status = #{inputDto.salesReturnStatus}
        GROUP BY
            DATE_FORMAT(T2.create_time, '%m-%d')
    </select>

    <select id="getMonthRefundAmount" resultType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto">
        WITH RECURSIVE DateRange AS (
            SELECT
                STR_TO_DATE(#{inputDto.startDate}, '%Y-%m-%d') AS date
            UNION ALL
            SELECT
                DATE_ADD(date, INTERVAL 1 DAY)
            FROM
                DateRange
            WHERE
                DATE <![CDATA[<]]> STR_TO_DATE(#{inputDto.endDate}, '%Y-%m-%d')
        )
        SELECT
            DATE_FORMAT(MAX(T1.date), '%m.%d') AS date,
            COALESCE(SUM(CAST(T3.return_amount AS DECIMAL(10,2))), 0) AS amount
        FROM
            DateRange T1
                LEFT JOIN mst_order T2
                          ON DATE(T2.create_time) = T1.date
                              AND T2.order_status = #{inputDto.orderStatus}
                              AND T2.consumer_store_id = #{inputDto.storeId}
                LEFT JOIN mst_sales_return T3
                          ON T3.order_id = T2.order_id
                              AND T3.sales_retrun_status = #{inputDto.salesReturnStatus}
        GROUP BY
            FLOOR(DATEDIFF(T1.date, STR_TO_DATE(#{inputDto.startDate}, '%Y-%m-%d')) / 5)
    </select>

    <select id="getHourRefundAmount" resultType="jp.co.nttdata.sz.web.service.orderopt.dto.WeekSalesAmountDto">
        WITH RECURSIVE HourRange AS (
            SELECT
                DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 23 HOUR), '%Y-%m-%d %H:00:00') AS hour_time
            UNION ALL
            SELECT
                DATE_FORMAT(DATE_ADD(STR_TO_DATE(hour_time, '%Y-%m-%d %H:%i:%s'), INTERVAL 1 HOUR), '%Y-%m-%d %H:00:00')
            FROM
                HourRange
            WHERE
                STR_TO_DATE(hour_time, '%Y-%m-%d %H:%i:%s') <![CDATA[<]]> NOW()
        )
        SELECT
            CONCAT(
                    DATE_FORMAT(MIN(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s')),'%H:00')
            ) AS date,
            COALESCE(SUM(CAST(T3.return_amount AS DECIMAL(10,2))), 0) AS amount
        FROM
            HourRange T1
                LEFT JOIN mst_order T2
                          ON DATE_FORMAT(T2.create_time, '%Y-%m-%d %H') = DATE_FORMAT(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d %H')
                              AND T2.order_status = #{inputDto.orderStatus}
                              AND T2.consumer_store_id = #{inputDto.storeId}
                LEFT JOIN mst_sales_return T3
                          ON T3.order_id = T2.order_id
                              AND T3.sales_retrun_status = #{inputDto.salesReturnStatus}
        GROUP BY
            FLOOR(HOUR(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s')) / 4)
        ORDER BY
            MIN(STR_TO_DATE(T1.hour_time, '%Y-%m-%d %H:%i:%s'))
    </select>
</mapper>
