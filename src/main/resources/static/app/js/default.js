function commonResultDecode(result, success, failed) {
    var data = eval(result);
    if (data.rt != 0) {
        success(data.messages);
    } else {
        failed(data.messages);
    }
}

function commonResultDecode2(result, success, failed) {
    var data = eval("["+result+"]");
    if (data[0].rt != 0) {
        success(data[0].data);
    } else {
        failed(data[0].messages);
    }
}

function isEmpty(item) {
    if (item != undefined && item != null && item != "") {
        return false;
    }
    return true;
}

function isCollectionEmpty(item) {
    if (item != undefined && item != null && item.length != 0) {
        return false;
    }
    return true;
}

function messageToDo() {
    layer.msg("<span style='color: black'>開発中...</span>");
}

function mesageGetFailed() {
    layer.msg("数据取得失败。", {icon: 0});
}

function showLoading() {
    var load = layer.load(1, {
        shade: [0.1, '#fff']
    });
    return load;
}

function showLoadingAutoClose() {
    var load = layer.load(1, {
        shade: [0.1, '#fff']
    });
    setTimeout(function () {
        layer.close(load);
    }, 1000);
}


function closelayerLoading() {
    $(".layui-layer-loading").each(function (index, element) {
        var id = $(element).attr("times");
        if (!isEmpty(id)) {
            layer.close(id);
        }
    })
}
function appendZero(data) {
    if (isEmpty(data)) {
        return 0;
    }
    return Math.round(Number(trim(data)))
}
function numberFormat(data) {
    if (isEmpty(data)) {
        return 0;
    }
    return Number(trim(data));
}

function roundData(data) {
    if (isEmpty(data)) {
        return 0;
    }
    data = trim(data);
    if (data.indexOf(".") == 0) {
        return "0" + data;
    }
    return data
}

function trim(str) {
    return str.toString().replace(/(^\s*)|(\s*$)/g, "");
}

function closeLayerPage() {
    $(".layui-layer-page").each(function (index, element) {
        var id = $(element).attr("times");
        if (!isEmpty(id)) {
            layer.close(id);
        }
    })
}

function closeLayerAnim() {
    $(".layer-anim").each(function (index, element) {
        var id = $(element).attr("times");
        if (!isEmpty(id)) {
            layer.close(id);
        }
    })
}

function dataChange(time) {
    var year = time._d.getFullYear();
    var month = time._d.getDate();
    var day = time._d.getDay();
    return year + "/" + (month < 10 ? "0" : "") + month + "/" + (day < 10 ? "0" : "") + day + "/";
}


/*AJAX*/
/**
 *
 * @param nid
 * @param url
 * @param callback
 * @param data  "entity_id=nid"
 * @param extendUrl
 */
function mAjaxGet(extendUrl, callback) {
    var load = showLoading();
    $.ajax({
        type: 'get',
        timeout: 30000,
        async: false,
        contentType: 'application/json',
        beforeSend: function (xhr) {
            xhr.setRequestHeader('Access-Control-Allow-Origin', '*');
        },
        url: extendUrl,
        success: function (result) {
            callback(result);
            layer.close(load);
        },
        complete: function (xhr, status) {

        }, error: function (XMLHttpRequest, textStatus, errorThrown) {
            layer.close(load);
        }
    });
};


/*AJAX*/
/**
 *
 * @param nid
 * @param url
 * @param callback
 * @param data  "entity_id=nid"
 * @param extendUrl
 */
function mAjax(extendUrl, callback, data, error) {
    var load = showLoading();
    $.ajax({
        type: 'POST',
        timeout: 30000,
        async: false,
        contentType: 'application/json',
        beforeSend: function (xhr) {
            xhr.setRequestHeader('Access-Control-Allow-Origin', '*');
        },
        url: extendUrl,
        dataType: 'json',
        data: JSON.stringify(data),
        success: function (result) {
            callback(result);
            layer.close(load);
        },
        complete: function (xhr, status) {

        }, error: function (XMLHttpRequest, textStatus, errorThrown) {
            if (!isEmpty(error)) {
                error(XMLHttpRequest);
            } else {
                layer.alert('连接失败', {
                    title: "メッセージ",
                    skin: 'layui-layer-molv'
                    ,
                    closeBtn: 0,
                    btn: ['确定']
                }, function () {
                    window.location.href = "./error";
                });
            }
            layer.close(load);
        }
    });
};

function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

/**
 * FormToJson
 */
(function ($) {
    $.fn.serializeJson = function () {
        var serializeObj = {};
        var array = this.serializeArray();
        var str = this.serialize();
        $(array).each(function () {
            if (serializeObj[this.name]) {
                if ($.isArray(serializeObj[this.name])) {
                    serializeObj[this.name].push(this.value);
                }
                else {
                    serializeObj[this.name] = [serializeObj[this.name], this.value];
                }
            }
            else {
                serializeObj[this.name] = this.value;
            }
        });
        return serializeObj;
    };
})(jQuery);